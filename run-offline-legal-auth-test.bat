@echo off
echo 正在运行线下法人授权测试...

REM 设置Java路径（如果需要）
set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_181
set PATH=%JAVA_HOME%\bin;%PATH%

REM 设置项目路径
set PROJECT_DIR=%~dp0
cd /d %PROJECT_DIR%

REM 构建classpath
set CLASSPATH=%PROJECT_DIR%src\main\java;%PROJECT_DIR%src\test\java
set CLASSPATH=%CLASSPATH%;%PROJECT_DIR%history\paas-sdk-3.0.20.jar
set CLASSPATH=%CLASSPATH%;%PROJECT_DIR%lib\*

REM 添加TestNG和其他依赖（需要下载）
REM 注意：您需要下载testng.jar并放在lib目录中

echo 使用的Classpath: %CLASSPATH%
echo.

REM 编译测试类
echo 正在编译测试类...
javac -cp "%CLASSPATH%" src\test\java\test\auth\CreateOfflineLegalAuthTest.java

if %ERRORLEVEL% neq 0 (
    echo 编译失败！请检查依赖和代码。
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 运行测试（需要TestNG）
echo 正在运行测试...
echo 注意：需要TestNG jar包才能运行
echo 请使用IDE或Maven来运行测试

pause
