INFO main com.util.Client - 注册到：**********
INFO main esign.utils.httppool.HttpPool - configure pool. 
set connection pool. [maxTotalConnection] => 10
set connection pool. [defaultMaxPerRouteConnection] => 2
set connection pool. [routes] => 0
INFO main esign.utils.httppool.HttpPool - configure pool. 
set connection pool. [maxTotalConnection] => 200
set connection pool. [defaultMaxPerRouteConnection] => 200
set connection pool. [routes] => 0
INFO main com.util.Client - **********注册成功
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除个人账号：
INFO main com.util.operation.Operations - 查询测试猫猫零，返回：{"accountId":"4F687CEAA56A4EC08FDF1C550F94201A","accountType":"PERSON","errCode":0,"errShow":false,"idNo":"432427189712163920","idNoType":"MAINLAND","msg":"成功","name":"测试猫猫零"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - 4F687CEAA56A4EC08FDF1C550F94201A已被成功删除
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除法人账号：
INFO main com.util.operation.Operations - 查询彭蛋花，返回：{"accountId":"8DAFA498981B4BCCA04EF6F6AC88AF09","accountType":"PERSON","errCode":0,"errShow":false,"idNo":"432427189510305353","idNoType":"MAINLAND","msg":"成功","name":"彭蛋花"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - 8DAFA498981B4BCCA04EF6F6AC88AF09已被成功删除
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除企业账号：
INFO main com.util.operation.Operations - 查询esigntest彭蛋花企业，返回：{"accountId":"F4A9A752917F4C9A849AD7C2F763AA30","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000066135592GF","idNoType":"MERGE","msg":"成功","name":"esigntest彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - F4A9A752917F4C9A849AD7C2F763AA30已被成功删除
INFO main com.util.operation.Operations - 查询彭蛋花企业，返回：{"accountId":"A951D3C715C34FE18211EC9535020894","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000004975632UX","idNoType":"MERGE","msg":"成功","name":"彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - A951D3C715C34FE18211EC9535020894已被成功删除
INFO main com.util.http.HttpRequest - POST	http://sdk.smlk8s.esign.cn/mock/phones
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - User-Agent : Apache-HttpClient/4.5.14 (Java/1.8.0_441)
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - Content-MD5 : Vk4dLhz4zgeBPCjnXA040Q==
INFO main com.util.http.HttpRequest - POST data:
INFO main com.util.http.HttpRequest - {"env":"${env}","list":["19012580018","19012580000"]}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - Vary : Access-Control-Request-Headers
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 08:44:38 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json
INFO main com.util.http.HttpRequest - {"list":[{"success":true,"code":2,"message":"有效期超过1天，不进行操作！"},{"success":true,"code":2,"message":"有效期超过1天，不进行操作！"}]}
INFO main com.util.http.HttpRequest - POST	http://in-testopenapi.tsign.cn/v2/identity/auth/api/individual/telecom3Factors
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - X-Tsign-Service-Group : ${env}
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Id : **********
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Secret : bceca44f79f96025077627e22f9c232c
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - User-Agent : Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36
INFO main com.util.http.HttpRequest - Content-MD5 : 2URacHOM8BlsVy6hNkGYfg==
INFO main com.util.http.HttpRequest - Accept : *
INFO main com.util.http.HttpRequest - X-Tsign-Open-Auth-Mode : Signature
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Timestamp : 1748249085144
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Signature : P7jqHE+IozmLVQuqCDb2rZ2AUeU7ZEszg1f0EJxCikE=
INFO main com.util.http.HttpRequest - POST data:
INFO main com.util.http.HttpRequest - {"idNo":"432427189712163920","mobileNo":"19012580000","name":"测试猫猫零","notifyUrl":"https://libaohui.com.cn/callback/ding","refVerifyID":"","source":2}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Server : openresty
INFO main com.util.http.HttpRequest - X-Tsign-Elapse-Time : 439
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - X-Tsign-Trace-Id : 10100016160T286T17482490852243471
INFO main com.util.http.HttpRequest - X-Application-Context : application
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 08:44:45 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - {"code":0,"message":"成功","data":{"flowId":"3917699471190272337","behavior":{"source":1,"refFlowId":"3869540205032115844","refFlowTime":1745378569000,"refFlowExpired":1776914569000}}}
INFO main com.util.operation.Operations - 等待3000毫秒
INFO main com.util.http.HttpRequest - PUT	http://in-testopenapi.tsign.cn/v2/identity/auth/pub/individual/3917699471190272337/telecom3Factors
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - X-Tsign-Service-Group : ${env}
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Id : **********
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Secret : bceca44f79f96025077627e22f9c232c
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - User-Agent : Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36
INFO main com.util.http.HttpRequest - Content-MD5 : VbzYbfNmdzbXf8gVu89Vbg==
INFO main com.util.http.HttpRequest - Accept : *
INFO main com.util.http.HttpRequest - X-Tsign-Open-Auth-Mode : Signature
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Timestamp : 1748249088697
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Signature : wwzclPqwF32kwWXmAcrNerp60eYD4Q1gKx3/8pPziTk=
INFO main com.util.http.HttpRequest - PUT data:
INFO main com.util.http.HttpRequest - {"authcode":"123456"}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Server : openresty
INFO main com.util.http.HttpRequest - X-Tsign-Elapse-Time : 320
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - X-Tsign-Trace-Id : 10100016160T286T17482490887443527
INFO main com.util.http.HttpRequest - X-Application-Context : application
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 08:44:49 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - {"code":0,"message":"成功","data":null}
INFO main com.util.operation.Operations - 等待1000毫秒
INFO main test.auth.CreateOfflineLegalAuthTest - 发起线下法人授权平台签署：{"authType":1,"fileType":"天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！","identityAuthId":"3917699471190272337","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"FDCFB72526164E42AA191897B940FB16","sealScope":"天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！","transactorAccountId":"A9A045C50DCA489C911854B108B8A95A"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: organizeId不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: organizeId不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateAuthParam.check(OfflineCreateAuthParam.java:112)
	at com.timevale.esign.paas.tech.service.a.d.createAuthOffline(AuthServiceImpl.java:188)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - organizeId为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: organizeId不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - identityAuthId为空场景返回：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - transactorAccountId为空场景返回：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - name为空场景返回：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - idNo为空场景返回：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - idNoType为空场景返回：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: 授权至经办人时，个人账号不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: 授权至经办人时，个人账号不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateAuthParam.checkPerSonId(OfflineCreateAuthParam.java:127)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateAuthParam.check(OfflineCreateAuthParam.java:115)
	at com.timevale.esign.paas.tech.service.a.d.createAuthOffline(AuthServiceImpl.java:188)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 授权给经办人账号-不传经办人oid场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: 授权至经办人时，个人账号不能为空"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权参数】：{"authType":1,"fileType":"借贷协议","identityAuthId":"3917699471190272337","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"FDCFB72526164E42AA191897B940FB16","sealScope":"合同专用章","transactorAccountId":"A9A045C50DCA489C911854B108B8A95A"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权结果】：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人参数】：{"authType":2,"fileType":"借贷协议","identityAuthId":"3917699471190272337","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"FDCFB72526164E42AA191897B940FB16","personId":"A9A045C50DCA489C911854B108B8A95A","sealScope":"合同专用章","transactorAccountId":"A9A045C50DCA489C911854B108B8A95A"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人结果】：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人参数】：{"authType":2,"fileType":"借贷协议","identityAuthId":"3917699471190272337","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"FDCFB72526164E42AA191897B940FB16","personId":"A9A045C50DCA489C911854B108B8A95A","sealScope":"合同专用章","transactorAccountId":"A9A045C50DCA489C911854B108B8A95A"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人结果】：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权参数】：{"authType":1,"fileType":"借贷协议","identityAuthId":"3917699471190272337","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"FDCFB72526164E42AA191897B940FB16","sealScope":"合同专用章","transactorAccountId":"A9A045C50DCA489C911854B108B8A95A"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权结果】：{"errCode":331002,"errShow":false,"msg":"参数非法: validDate不能为空且应该大于当前时间"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
ERROR main esign.utils.bean.ResultUtil - failed for code: 331002, msg: 参数非法: validDate不能为空且应该大于当前时间
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除个人账号：
INFO main com.util.operation.Operations - 查询测试猫猫零，返回：{"accountId":"A9A045C50DCA489C911854B108B8A95A","accountType":"PERSON","errCode":0,"errShow":false,"idNo":"432427189712163920","idNoType":"MAINLAND","msg":"成功","name":"测试猫猫零"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - A9A045C50DCA489C911854B108B8A95A已被成功删除
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除法人账号：
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"432427189510305353","idNoType":"MAINLAND","type":"PERSON"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除企业账号：
INFO main com.util.operation.Operations - 查询esigntest彭蛋花企业，返回：{"accountId":"FDCFB72526164E42AA191897B940FB16","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000066135592GF","idNoType":"MERGE","msg":"成功","name":"esigntest彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - FDCFB72526164E42AA191897B940FB16已被成功删除
INFO main com.util.operation.Operations - 查询彭蛋花企业，返回：{"accountId":"C7057F54491D44BFB8D9EE38893F92A9","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000004975632UX","idNoType":"MERGE","msg":"成功","name":"彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - C7057F54491D44BFB8D9EE38893F92A9已被成功删除
INFO main com.util.Client - 注册到：**********
INFO main esign.utils.httppool.HttpPool - configure pool. 
set connection pool. [maxTotalConnection] => 10
set connection pool. [defaultMaxPerRouteConnection] => 2
set connection pool. [routes] => 0
INFO main esign.utils.httppool.HttpPool - configure pool. 
set connection pool. [maxTotalConnection] => 200
set connection pool. [defaultMaxPerRouteConnection] => 200
set connection pool. [routes] => 0
INFO main com.util.Client - **********注册成功
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除个人账号：
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"432427189712163920","idNoType":"MAINLAND","type":"PERSON"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除法人账号：
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"432427189510305353","idNoType":"MAINLAND","type":"PERSON"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除企业账号：
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"9100000066135592GF","idNoType":"MERGE","type":"ORGAN"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"9100000004975632UX","idNoType":"MERGE","type":"ORGAN"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.http.HttpRequest - POST	http://sdk.smlk8s.esign.cn/mock/phones
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - User-Agent : Apache-HttpClient/4.5.14 (Java/1.8.0_441)
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - Content-MD5 : Vk4dLhz4zgeBPCjnXA040Q==
INFO main com.util.http.HttpRequest - POST data:
INFO main com.util.http.HttpRequest - {"env":"${env}","list":["19012580018","19012580000"]}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - Vary : Access-Control-Request-Headers
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 09:08:31 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json
INFO main com.util.http.HttpRequest - {"list":[{"success":true,"code":2,"message":"有效期超过1天，不进行操作！"},{"success":true,"code":2,"message":"有效期超过1天，不进行操作！"}]}
INFO main com.util.http.HttpRequest - POST	http://in-testopenapi.tsign.cn/v2/identity/auth/api/individual/telecom3Factors
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - X-Tsign-Service-Group : ${env}
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Id : **********
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Secret : bceca44f79f96025077627e22f9c232c
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - User-Agent : Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36
INFO main com.util.http.HttpRequest - Content-MD5 : 2URacHOM8BlsVy6hNkGYfg==
INFO main com.util.http.HttpRequest - Accept : *
INFO main com.util.http.HttpRequest - X-Tsign-Open-Auth-Mode : Signature
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Timestamp : 1748250513332
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Signature : P7jqHE+IozmLVQuqCDb2rZ2AUeU7ZEszg1f0EJxCikE=
INFO main com.util.http.HttpRequest - POST data:
INFO main com.util.http.HttpRequest - {"idNo":"432427189712163920","mobileNo":"19012580000","name":"测试猫猫零","notifyUrl":"https://libaohui.com.cn/callback/ding","refVerifyID":"","source":2}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Server : openresty
INFO main com.util.http.HttpRequest - X-Tsign-Elapse-Time : 406
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - X-Tsign-Trace-Id : 10100016160T285T17482505134189975
INFO main com.util.http.HttpRequest - X-Application-Context : application
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 09:08:33 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - {"code":0,"message":"成功","data":{"flowId":"3917723431906847047","behavior":{"source":1,"refFlowId":"3869540205032115844","refFlowTime":1745378569000,"refFlowExpired":1776914569000}}}
INFO main com.util.operation.Operations - 等待3000毫秒
INFO main com.util.http.HttpRequest - PUT	http://in-testopenapi.tsign.cn/v2/identity/auth/pub/individual/3917723431906847047/telecom3Factors
INFO main com.util.http.HttpRequest - Request Headers:
INFO main com.util.http.HttpRequest - X-Tsign-Service-Group : ${env}
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Id : **********
INFO main com.util.http.HttpRequest - X-Tsign-Open-App-Secret : bceca44f79f96025077627e22f9c232c
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - User-Agent : Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36
INFO main com.util.http.HttpRequest - Content-MD5 : VbzYbfNmdzbXf8gVu89Vbg==
INFO main com.util.http.HttpRequest - Accept : *
INFO main com.util.http.HttpRequest - X-Tsign-Open-Auth-Mode : Signature
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Timestamp : 1748250516809
INFO main com.util.http.HttpRequest - X-Tsign-Open-Ca-Signature : 358nYVVu334ptkbGe7KP/C1BO3816U5uIl8FicZx1uw=
INFO main com.util.http.HttpRequest - PUT data:
INFO main com.util.http.HttpRequest - {"authcode":"123456"}
INFO main com.util.http.HttpRequest - Response Headers:
INFO main com.util.http.HttpRequest - Transfer-Encoding : chunked
INFO main com.util.http.HttpRequest - Server : openresty
INFO main com.util.http.HttpRequest - X-Tsign-Elapse-Time : 503
INFO main com.util.http.HttpRequest - Connection : keep-alive
INFO main com.util.http.HttpRequest - X-Tsign-Trace-Id : 10100016160T286T17482505168561565
INFO main com.util.http.HttpRequest - X-Application-Context : application
INFO main com.util.http.HttpRequest - Date : Mon, 26 May 2025 09:08:37 GMT
INFO main com.util.http.HttpRequest - Content-Type : application/json;charset=UTF-8
INFO main com.util.http.HttpRequest - {"code":0,"message":"成功","data":null}
INFO main com.util.operation.Operations - 等待1000毫秒
INFO main test.auth.CreateOfflineLegalAuthTest - 发起线下法人授权平台签署：{"authType":1,"encrypt":false,"fileType":"天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！","identityAuthId":"3917723431906847047","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"A4DB408288D1490BACB54A0A5F065C82","sealScope":"天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！","transactorAccountId":"D674ECFC08EB4748AA47F8C8173D34ED"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalAuthTest(CreateOfflineLegalAuthTest.java:287)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: organizeId不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: organizeId不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:167)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - organizeId为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: organizeId不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - identityAuthId为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - transactorAccountId为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - name为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - idNo为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - idNoType为空场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalRepExceptTest(CreateOfflineLegalAuthTest.java:262)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 授权给经办人账号-不传经办人oid场景返回：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权参数】：{"authType":1,"encrypt":false,"fileType":"借贷协议","identityAuthId":"3917723431906847047","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"A4DB408288D1490BACB54A0A5F065C82","sealScope":"合同专用章","transactorAccountId":"D674ECFC08EB4748AA47F8C8173D34ED"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalAuthToPlatform(CreateOfflineLegalAuthTest.java:761)
	at test.auth.CreateOfflineLegalAuthTest.testCreateOfflineLegalAuthSimple(CreateOfflineLegalAuthTest.java:720)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权结果】：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人参数】：{"authType":2,"encrypt":false,"fileType":"借贷协议","identityAuthId":"3917723431906847047","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"A4DB408288D1490BACB54A0A5F065C82","personId":"D674ECFC08EB4748AA47F8C8173D34ED","sealScope":"合同专用章","transactorAccountId":"D674ECFC08EB4748AA47F8C8173D34ED"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.testCreateOfflineLegalAuthToPersonComplete(CreateOfflineLegalAuthTest.java:686)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人结果】：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人参数】：{"authType":2,"encrypt":false,"fileType":"借贷协议","identityAuthId":"3917723431906847047","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"A4DB408288D1490BACB54A0A5F065C82","personId":"D674ECFC08EB4748AA47F8C8173D34ED","sealScope":"合同专用章","transactorAccountId":"D674ECFC08EB4748AA47F8C8173D34ED"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.createOfflineLegalAuthToPerson(CreateOfflineLegalAuthTest.java:779)
	at test.auth.CreateOfflineLegalAuthTest.testCreateOfflineLegalAuthToPersonSimple(CreateOfflineLegalAuthTest.java:736)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权经办人结果】：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权参数】：{"authType":1,"encrypt":false,"fileType":"借贷协议","identityAuthId":"3917723431906847047","notifyUrl":"https://libaohui.com.cn/callback/ding","organizeId":"A4DB408288D1490BACB54A0A5F065C82","sealScope":"合同专用章","transactorAccountId":"D674ECFC08EB4748AA47F8C8173D34ED"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.testCreateOfflineLegalAuthToPlatformComplete(CreateOfflineLegalAuthTest.java:636)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 【发起线下法人授权结果】：{"errCode":10001,"errShow":false,"msg":"参数非法: name不能为空"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.uploadLegalAuthFileByPathTest(CreateOfflineLegalAuthTest.java:606)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
ERROR main esign.utils.bean.ResultUtil - failed for code: 10001, msg: 参数非法: name不能为空
ERROR main esign.utils.bean.ResultUtil - exception:
esign.utils.exception.SuperException: 参数非法: name不能为空
	at esign.utils.exception.collector.meta.ErrorMeta.e(ErrorMeta.java:74)
	at com.timevale.esign.paas.tech.bean.request.OfflineCreateLegalRepAuthParam.check(OfflineCreateLegalRepAuthParam.java:170)
	at com.timevale.esign.paas.tech.service.a.d.createOfflineLegalAuth(AuthServiceImpl.java:323)
	at test.auth.CreateOfflineLegalAuthTest.uploadLegalAuthFileExceptionTest(CreateOfflineLegalAuthTest.java:541)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
	at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
	at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
	at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
	at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
	at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
	at org.testng.TestRunner.privateRun(TestRunner.java:648)
	at org.testng.TestRunner.run(TestRunner.java:505)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:450)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:415)
	at org.testng.SuiteRunner.run(SuiteRunner.java:364)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:84)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1208)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1137)
	at org.testng.TestNG.runSuites(TestNG.java:1049)
	at org.testng.TestNG.run(TestNG.java:1017)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除个人账号：
INFO main com.util.operation.Operations - 查询测试猫猫零，返回：{"accountId":"D674ECFC08EB4748AA47F8C8173D34ED","accountType":"PERSON","errCode":0,"errShow":false,"idNo":"432427189712163920","idNoType":"MAINLAND","msg":"成功","name":"测试猫猫零"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - D674ECFC08EB4748AA47F8C8173D34ED已被成功删除
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除法人账号：
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除账号方法执行异常：查询{"idNo":"432427189510305353","idNoType":"MAINLAND","type":"PERSON"}，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main test.auth.CreateOfflineLegalAuthTest - 开始删除企业账号：
INFO main com.util.operation.Operations - 查询esigntest彭蛋花企业，返回：{"accountId":"A4DB408288D1490BACB54A0A5F065C82","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000066135592GF","idNoType":"MERGE","msg":"成功","name":"esigntest彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - A4DB408288D1490BACB54A0A5F065C82已被成功删除
INFO main com.util.operation.Operations - 查询彭蛋花企业，返回：{"accountId":"325815E111FE414E91CF3E0F4F091D44","accountType":"ORGAN","errCode":0,"errShow":false,"idNo":"9100000004975632UX","idNoType":"MERGE","msg":"成功","name":"彭蛋花企业"}
INFO main com.util.operation.Operations - 删除用户，返回：{"errCode":0,"errShow":false,"msg":"成功"}
ERROR main esign.utils.bean.ResultUtil - failed for code: 331009, msg: 账号不存在
INFO main com.util.operation.Operations - 删除后查询用户，返回：{"errCode":331009,"errShow":false,"msg":"账号不存在"}
INFO main com.util.operation.Operations - 325815E111FE414E91CF3E0F4F091D44已被成功删除
