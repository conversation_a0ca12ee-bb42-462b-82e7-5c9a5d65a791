<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="local" value="${local}"/><!--test:测试，sml:模拟，pro:正式-->
    <test verbose="3" name="back test">
        <packages>
            <package name="test"/>
<!--            <package name="test.account.person"/>-->
<!--            <package name="test.seal.personal"/>-->
<!--            <package name="test.seal.official"/>-->
            <package name="test.template"/>
        </packages>
    </test>
</suite>