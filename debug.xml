<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="title" value="快捷签核心链路结果通知"/>
    <parameter name="branch" value="${branch}"/>
    <parameter name="buildId" value="${buildId}"/>
    <parameter name="secret" value="${secret}"/>
    <parameter name="token" value="${token}"/>
    <parameter name="projectName" value="${projectName}"/>
    <listeners>
        <listener class-name="com.util.report.ExtentTestNGIReporterListener"/>
    </listeners>
    <test verbose="3" name="快捷签核心链路测试">
        <classes>
            <class name="test.sign.CoreChainTest">
                <methods>
                    <include name="createPsnAccount"/>
                    <include name="createOrgAccount"/>
                    <include name="updatePsnAccount"/>
                    <include name="updateOrgAccount"/>
                    <include name="clearAccount"/>
                </methods>
            </class>
        </classes>
    </test>
</suite>