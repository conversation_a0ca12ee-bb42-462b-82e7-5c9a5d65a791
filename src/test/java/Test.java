import com.timevale.esign.paas.tech.bean.request.FilePdfParam;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.util.Client;
import com.util.operation.Operations;

public class Test {
    public static void main(String[] args) {
        Client.setTimevaleData("test");
        ServiceClient serviceClient =
                Client.getClient("test", Client.getTimevaleData().getProject());
        FilePdfParam filePdfParam = new FilePdfParam();
        while (true) {
            filePdfParam.setSrcPdfFile(
                    "c:\\Users\\<USER>\\Downloads\\08月09日16时07分19秒725毫秒3.0一步发起.pdf");
            serviceClient.signVerifyService().localVerifyPdf(filePdfParam);
        }
    }
}
