package test.template;

import com.alibaba.fastjson2.JSONObject;
import com.timevale.esign.paas.tech.bean.bean.PdfTableBean;
import com.timevale.esign.paas.tech.bean.request.SignFilePdfParam;
import com.timevale.esign.paas.tech.bean.result.FileCreateFromTemplateResult;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.FontNames;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.ModifyDate;
import com.util.data.Names;
import com.util.file.FileUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class CreateFileFromTemplateTest {
    private byte[] streamFile;
    private byte[] streamFileWithEditPassword;
    private byte[] broken_stream;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String FILE_WITH_EDIT_PASSWORD = "./input/带编辑密码表单文件.pdf";
    private static final String FILE_WITH_OPEN_PASSWORD = "./input/带打开密码表单文件.pdf";
    private static final String BROKEN_FILE = "./input/文件已损坏.pdf";
    private static final String OUTPUT = "./output";
    private ServiceClient serviceClient;

    @BeforeClass
    @Parameters({"env"})
    public void setPersonAccountId(String env) {
        Client.setTimevaleData(env);
        serviceClient = Client.getClient(env, Client.getTimevaleData().getProject());
    }

    @DataProvider
    public Object[][] createFileFromTemplateTest() {
        return new Object[][] {
            {
                "本地PDF模板文件填充-文件路径",
                FILE_PATH,
                null,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                        put("1111", "22222");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-文件路径不存在",
                FILE_PATH + "111",
                null,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                60010,
                "读取异常，请检查文件正确性"
            },
            {
                "本地PDF模板文件填充-文件路径不存在-读取流",
                FILE_PATH + "111",
                streamFile,
                OUTPUT,
                "qqq111!@#",
                false,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-文件路径存在-读取流",
                FILE_PATH + "111",
                streamFileWithEditPassword,
                OUTPUT,
                "223456",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                800032,
                "模板文件填充失败:No message found for bad.user.password"
            },
            {
                "本地PDF模板文件填充-路径为空字符串",
                "",
                null,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                60001,
                "srcPdfFile与streamFile不能同时为空"
            },
            {
                "本地PDF模板文件填充-路径为空字符串读取字节流",
                "",
                streamFile,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-路径为null",
                null,
                null,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                60001,
                "srcPdfFile与streamFile不能同时为空"
            },
            {
                "本地PDF模板文件填充-路径为null读取字节流",
                null,
                streamFile,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-文件路径-带编辑密码-未传密码",
                FILE_WITH_EDIT_PASSWORD,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-文件路径-带编辑密码-密码错误",
                FILE_WITH_EDIT_PASSWORD,
                null,
                OUTPUT,
                "223456",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                800032,
                "模板文件填充失败:No message found for bad.user.password"
            },
            //            {
            //                "本地PDF模板文件填充-文件流-带编辑密码-密码传空",
            //                null,
            //                streamFileWithEditPassword,
            //                OUTPUT,
            //                null,
            //                true,
            //                new HashMap<String, Object>() {
            //                    {
            //                        put("签署方2", "杭州天谷科技有限公司");
            //                    }
            //                },
            //                800032,
            //                "模板文件填充失败:No message found for bad.user.password"
            //            },
            //            {
            //                "本地PDF模板文件填充-文件路径-带编辑密码-密码传空",
            //                FILE_WITH_EDIT_PASSWORD,
            //                null,
            //                OUTPUT,
            //                null,
            //                true,
            //                new HashMap<String, Object>() {
            //                    {
            //                        put("签署方2", "杭州天谷科技有限公司");
            //                    }
            //                },
            //                800032,
            //                "模板文件填充失败:No message found for bad.user.password"
            //            },
            {
                "本地PDF模板文件填充-文件路径-带打开密码-密码传空",
                FILE_WITH_OPEN_PASSWORD,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                800032,
                "模板文件填充失败:No message found for bad.user.password"
            },
            {
                "本地PDF模板文件填充-文件路径-带打开密码-密码传空",
                FILE_WITH_OPEN_PASSWORD,
                null,
                OUTPUT,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方2", "杭州天谷科技有限公司");
                    }
                },
                800032,
                "模板文件填充失败:No message found for bad.user.password"
            },
            {
                "本地PDF模板文件填充-未传输出路径-输出字节流",
                FILE_PATH,
                null,
                null,
                "qqq111!@#",
                true,
                new HashMap<String, Object>() {
                    {
                        put("签署方1", "杭州天谷科技有限公司");
                    }
                },
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-填充数据为空对象",
                FILE_PATH,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {},
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-填充数据为null",
                FILE_PATH,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {},
                0,
                "成功"
            },
            {
                "本地PDF模板文件填充-填充数据不存在",
                FILE_PATH,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {
                    {
                        put("11", "22");
                    }
                },
                0,
                "成功"
            },
            //            {
            //                "本地PDF模板文件填充-输出路径不存在",
            //                FILE_PATH,
            //                null,
            //                "/OUTPUT",
            //                null,
            //                true,
            //                new HashMap<String, Object>() {
            //                    {
            //                        put("11", "22");
            //                    }
            //                },
            //                0,
            //                "成功"
            //            },
            {
                "本地PDF模板文件填充-文件路径-文件损坏",
                BROKEN_FILE,
                null,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {
                    {
                        put("11", "22");
                    }
                },
                800032,
                "模板文件填充失败:No message found for pdf.header.not.found"
            },
            {
                "本地PDF模板文件填充-文件流-文件损坏",
                null,
                broken_stream,
                OUTPUT,
                null,
                true,
                new HashMap<String, Object>() {
                    {
                        put("11", "22");
                    }
                },
                800032,
                "模板文件填充失败:No message found for pdf.header.not.found"
            }
        };
    }

    //    @AfterClass
    //    private void clear() {
    //        File file = new File(OUTPUT);
    //        for (File f : file.listFiles()) {
    //            log.info("删除文件{}", f.getName());
    //            f.delete();
    //        }
    //    }

    @Test(dataProvider = "createFileFromTemplateTest", enabled = true)
    public void createFileFromTemplateTest(
            String caseName,
            String srcPdfFile,
            byte[] streamFile,
            String dstPdfFile,
            String password,
            boolean isFlat,
            Map<String, Object> txtFields,
            int errCode,
            String msg)
            throws IOException {
        SignFilePdfParam signFilePdfParam = new SignFilePdfParam();
        signFilePdfParam.setSrcPdfFile(srcPdfFile);
        signFilePdfParam.setStreamFile(streamFile);
        signFilePdfParam.setDstPdfFile(dstPdfFile);
        signFilePdfParam.setPassword(password);
        FileCreateFromTemplateResult fileCreateFromTemplateResult =
                serviceClient
                        .pdfDocumentService()
                        .createFileFromTemplate(signFilePdfParam, isFlat, txtFields);
        log.info(
                "请求参数{}，{}：{}",
                String.format(
                        "%s\t%s\t%s\t%s\t%s", srcPdfFile, dstPdfFile, password, isFlat, txtFields),
                caseName,
                JSONObject.toJSONString(fileCreateFromTemplateResult));
        // 检查错误码
        Assert.assertEquals(fileCreateFromTemplateResult.getErrCode(), errCode);
        // 检查错误文案
        Assert.assertTrue(fileCreateFromTemplateResult.getMsg().contains(msg));
        if (errCode == 0) {
            if (!StringUtils.isEmpty(signFilePdfParam.getDstPdfFile())) {
                if (!("stream.pdf").equals(signFilePdfParam.getFileName())) {
                    String dstFilePath =
                            signFilePdfParam.getFileName() == null
                                    ? signFilePdfParam.getDstPdfFile()
                                    : String.format(
                                            "%s%s%s",
                                            signFilePdfParam.getDstPdfFile(),
                                            File.separatorChar,
                                            signFilePdfParam.getFileName());
                    String expectedName =
                            dstFilePath.split("\\\\")[dstFilePath.split("\\\\").length - 1];
                    log.info("指定目标文件名，创建指定的文件名，预计为：{}", expectedName);
                    String actualName =
                            fileCreateFromTemplateResult.getDstPdfFile()
                                    .split("\\\\")[
                                    fileCreateFromTemplateResult
                                                    .getDstPdfFile()
                                                    .split("\\\\")
                                                    .length
                                            - 1];
                    log.info("实际创建的文件名为：{}", actualName);
                    // 指定目标文件时根据指定文件名生成文件
                    Assert.assertEquals(actualName, expectedName);
                } else {
                    String expectedName =
                            fileCreateFromTemplateResult.getDstPdfFile()
                                    .split("\\\\")[
                                    fileCreateFromTemplateResult
                                                    .getDstPdfFile()
                                                    .split("\\\\")
                                                    .length
                                            - 1];
                    log.info("未指定目标文件名，创建的文件名为：{}", expectedName);
                    // 未指定文件名时文件esign开头
                    Assert.assertTrue(expectedName.startsWith("esign"));
                }
            } else {
                // 未传输出地址不生成文件
                Assert.assertNull(fileCreateFromTemplateResult.getDstPdfFile());
                // 未传输出地址返回文件流
                Assert.assertNotNull(fileCreateFromTemplateResult.getStream());
                FileUtil.write(fileCreateFromTemplateResult.getStream(), OUTPUT + "/stream.pdf");
                // 文件流可以正常生成文件
                Assert.assertTrue(new File(OUTPUT + "/stream.pdf").exists());
            }
        }
    }

    @DataProvider
    public Object[][] 小米填充() {
        return new Object[][] {
            {3F, FontNames.SIMKAI},
            {20F, FontNames.NSIMSUN},
            {50F, FontNames.SIMFANG},
            {0F, FontNames.SIMHEI},
            {null, FontNames.SIMSUN}
        };
    }

    @Test(dataProvider = "小米填充")
    public void 小米填充(Float aFloat, FontNames fontNames) {
        SignFilePdfParam signFilePdfParam = new SignFilePdfParam();
        signFilePdfParam.setSrcPdfFile("input/模板.pdf");
        DecimalFormat decimalFormat = new DecimalFormat("0");
        signFilePdfParam.setDstPdfFile(
                "output/"
                        + fontNames.getName()
                        + (aFloat == null ? "" : "+" + decimalFormat.format(aFloat))
                        + ".pdf");
        Map<String, Object> txtFields = new HashMap<>();
        txtFields.put("policyNo", UUID.randomUUID());
        txtFields.put("outContractId", UUID.randomUUID());
        txtFields.put("sumReportAmount", "￥12,345,678.90");
        txtFields.put("sumReportAmountInWords", "人民币壹万两仟叁佰肆拾伍元陆毛柒分整");
        txtFields.put("reportDate", ModifyDate.getCurrentDate());
        txtFields.put("userName", Names.getChineseName());
        txtFields.put("idNumMask", IdCard.getIdNo());
        txtFields.put("encashDate", ModifyDate.getModifyDate(-3));
        txtFields.put("loanAmount", "人民币壹万两仟叁佰肆拾伍元陆毛柒分整");
        List<PdfTableBean> pdfTableBeanList = new ArrayList<>();
        PdfTableBean pdfTableBean = new PdfTableBean();
        pdfTableBean.setTableName("我是表头");
        pdfTableBean.setColumnWidthProportion(new float[] {1.0F, 1.0F, 1.0F});
        pdfTableBean.setTableHeader(new String[] {"第一列", "第二列", "第三列"});
        pdfTableBean.setCreateType(1);
        pdfTableBean.setKeyword("还款理赔明细表");
        List<String> row = Arrays.asList("第一列的数据", "第二列的数据", "第三列的数据");
        List<List<String>> rows = new ArrayList<>();
        rows.add(row);
        pdfTableBean.setTableDatas(rows);
        pdfTableBeanList.add(pdfTableBean);
        pdfTableBean.setCellFontSize(5F);
        pdfTableBean.setHeaderFontSize(9F);
        FileCreateFromTemplateResult fileCreateFromTemplateResult =
                serviceClient
                        .pdfDocumentService()
                        .createFileFromTemplate(
                                signFilePdfParam,
                                true,
                                txtFields,
                                fontNames,
                                aFloat,
                                pdfTableBeanList);
        log.info("{}", JSONObject.toJSONString(fileCreateFromTemplateResult));
    }
}
