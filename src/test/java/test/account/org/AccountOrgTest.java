package test.account.org;

import com.alibaba.fastjson2.JSONObject;
import com.timevale.esign.paas.tech.bean.request.OrganizeParam;
import com.timevale.esign.paas.tech.bean.result.AddAccountResult;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.IdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.OrganRegTypeEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class AccountOrgTest {

    @DataProvider
    public Object[][] addGetUpdateDeleteTest() {
        return new Object[][] {
            {
                "身份证-正常流程-增删改查",
                IdNoTypeEnum.MAINLAND,
                "address1",
                true,
                0,
                AllIdNoTypeEnum.MAINLAND,
                "19",
                "address2",
                true,
                0,
                "成功"
            },
        };
    }

    @DataProvider
    public Object[][] OrganizeParamExceptionTest() throws Exception {
        String orgCode = IdCard.getSocialCode("91");
        boolean encrypt = true;
        String name = Names.getEnglishName();
        return new Object[][] {
            {"name为null", null, OrganRegTypeEnum.REGCODE, orgCode, encrypt, 20001, "name不能为空"},
            {"name为空", "", OrganRegTypeEnum.REGCODE, orgCode, encrypt, 20001, "name不能为空"},
            {"regType为null", name, null, orgCode, encrypt, 20001, "regType不能为空"},
            {"正常用例", name, OrganRegTypeEnum.MERGE, orgCode, encrypt, 0, "0"},
        };
    }

    private ServiceClient serviceClient;

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        Client.setTimevaleData(env);
        serviceClient = Client.getClient(env, Client.getTimevaleData().getProject());
    }

    @Test(dataProvider = "OrganizeParamExceptionTest")
    public void OrganizeParamExceptionTest(
            String caseName,
            String name,
            OrganRegTypeEnum regType,
            String orgCode,
            boolean encrypt,
            int errCode,
            String msg) {
        OrganizeParam OrganizeParam = new OrganizeParam();
        OrganizeParam.setRegType(regType);
        OrganizeParam.setOrgCode(orgCode);
        OrganizeParam.setEncrypt(encrypt);
        OrganizeParam.setName(name);
        AddAccountResult addAccountResult =
                serviceClient.accountService().addAccount(OrganizeParam);
        log.info("{}场景返回：{}", caseName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), errCode);
        if (addAccountResult.getErrCode() != 0) {
            Assert.assertTrue(addAccountResult.getMsg().contains(msg));
        }
    }

    @DataProvider
    public Object[][] UpdateOrganizeParam() throws Exception {
        String orgCode = IdCard.getSocialCode("91");
        boolean encrypt = true;
        String name = Names.getEnglishName();
        return new Object[][] {{"name为null", name, OrganRegTypeEnum.REGCODE, orgCode, encrypt}};
    }
}
