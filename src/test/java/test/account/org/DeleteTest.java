package test.account.org;

import com.alibaba.fastjson2.JSONObject;
import com.timevale.esign.paas.tech.bean.request.UpdateOrganizeParam;
import com.timevale.esign.paas.tech.bean.result.Result;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.util.Client;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class DeleteTest {
    @BeforeClass
    @Parameters({"env"})
    private void init(String env) {
        Client.setTimevaleData(env);
        serviceClient = Client.getClient(env, Client.getTimevaleData().getProject());
    }

    private ServiceClient serviceClient;

    @DataProvider
    public Object[][] deleteAccountCase() {
        return new Object[][] {
            {"accountId为null", null, 10001, "accountId为空"},
            {"accountId为空", "", 10001, "accountId为空"},
            {"accountId不存在", "****************", 331003, "账号信息异常或不存在"},
            {"其他应用的accountId", "orgAccountFromOtherProject", 331003, "账号信息异常或不存在"},
        };
    }

    @Test(dataProvider = "deleteAccountCase")
    public void deleteAccountExceptTest(
            String caseName, String AccountId, int errCode, String msg) {
        UpdateOrganizeParam UpdateOrganizeParam = new UpdateOrganizeParam();
        UpdateOrganizeParam.setAccountId(AccountId);
        UpdateOrganizeParam.setName("3213212");
        Result result = serviceClient.accountService().deleteAccount(AccountId);
        log.info("{}场景返回：{}", caseName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), errCode);
        Assert.assertTrue(result.getMsg().contains(msg));
    }
}
