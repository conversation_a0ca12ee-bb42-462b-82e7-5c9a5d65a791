package test.account.person;

import com.alibaba.fastjson2.JSONObject;
import com.api.support.base_billing_manager.v2.accounts.account.Billings;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.PersonParam;
import com.timevale.esign.paas.tech.bean.request.PersonSignParam;
import com.timevale.esign.paas.tech.bean.request.UpdatePersonParam;
import com.timevale.esign.paas.tech.bean.result.AccountInfoResult;
import com.timevale.esign.paas.tech.bean.result.AddAccountResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.Result;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.IdNoTypeEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;
import com.util.data.PhoneNum;
import com.util.data.param.Params;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.util.UUID;

@Slf4j
public class AccountTest {
    private ServiceClient serviceClient0000;
    private ServiceClient serviceClient1000;
    private ServiceClient serviceClient1110;
    String phone = "***********";
    String bizTemplateCode = "realname_verification_code_with_agreement";
    private Project project0000;
    private Project project1000;
    private Project project1110;
    private static final String FILE_PATH = "./input/表单文件.pdf";

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        Client.setTimevaleData(env);
        project0000 = Client.getTimevaleData().getProject();
        project1000 = Client.getTimevaleData().getNewVersionCreateCert();
        project1110 =
                Client.getTimevaleData()
                        .getNewVersionCreateCertAndRealnameEvidencePointAndOfflineAuthTransactorWill();
        serviceClient0000 = Client.getClient(env, project0000);
        serviceClient1000 = Client.getClient(env, project1000);
        serviceClient1110 = Client.getClient(env, project1110);
    }

    @BeforeMethod
    public void clear() {
        Operations.clearFile();
    }

    @DataProvider
    public Object[][] addGetUpdateDeleteTest() {
        String idNo1 = IdCard.getIdNo();
        String name1 = Names.getChineseName();
        String mobile1 = PhoneNum.getPhoneNum();
        String email1 = mobile1 + "@esign.cn";
        String mobile2 = PhoneNum.getPhoneNum();
        String email2 = mobile2 + "@esign.cn";
        return new Object[][] {
            {
                "身份证-正常流程-增删改查",
                IdNoTypeEnum.MAINLAND,
                idNo1,
                name1,
                mobile1,
                email1,
                "address1",
                true,
                0,
                AllIdNoTypeEnum.MAINLAND,
                "19",
                mobile2,
                email2,
                "address2",
                true,
                0,
                "成功"
            },
        };
    }

    @DataProvider
    public Object[][] personParamExceptionTest() {
        String idNo = IdCard.getIdNo();
        String name = Names.getChineseName();
        return new Object[][] {
            {"正常", IdNoTypeEnum.MAINLAND, idNo, name, true, 0, "成功"},
            {"无证件类型", null, idNo, name, true, 20001, "idNoType为空"},
            {"身份证-无证件号", IdNoTypeEnum.MAINLAND, null, name, true, 20001, "idNo为空"},
            {"身份证-证件号为空字符串", IdNoTypeEnum.MAINLAND, "", name, true, 20001, "idNo为空"},
            {"身份证-证件号过长", IdNoTypeEnum.MAINLAND, idNo + "1", name, true, 331002, "个人证件号与证件类型不匹配"},
            {
                "身份证-证件号过短",
                IdNoTypeEnum.MAINLAND,
                "14118220100810572",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-证件号格式非法",
                IdNoTypeEnum.MAINLAND,
                "14118220100810572799",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-证件号格式HK,非H开头",
                IdNoTypeEnum.HONGKONG,
                "141182201008105727",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-证件号格式Macao,非M开头",
                IdNoTypeEnum.MACAO,
                "141182201008105727",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-证件号格式taiwan,非10位",
                IdNoTypeEnum.MACAO,
                "141182201008105727",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-证件号格式PASSPORT，异常",
                IdNoTypeEnum.MACAO,
                "141182201008105727",
                name,
                true,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "身份证-OTHER，异常",
                IdNoTypeEnum.OTHER,
                "1141182201008105727",
                name,
                true,
                1500012,
                "账号部分字段冲突"
            },
            {"身份证-类型null", null, "141182201008105727", name, true, 20001, "idNoType为空"},
            {"名字null", IdNoTypeEnum.MAINLAND, idNo, null, true, 20001, "name不能为空"},
            {"名字为空", IdNoTypeEnum.MAINLAND, idNo, "", true, 20001, "name不能为空"},
            {
                "名字长度81",
                IdNoTypeEnum.MAINLAND,
                idNo,
                "111111111111111111111111111111111111111111111111111111111111111111111111111111111",
                true,
                20001,
                "个人姓名只支持1到80个字符"
            },
        };
    }

    @DataProvider
    public Object[][] personParamNormalTest() {
        String mobile = PhoneNum.getPhoneNum();
        String email = mobile + "@esign.cn";
        return new Object[][] {
            {
                "正常数据",
                IdNoTypeEnum.MAINLAND,
                "340822199712270158",
                "宣刚义",
                "13111111111",
                email,
                "address",
                true,
                20001,
                "idNo为空"
            },
        };
    }

    @Test(dataProvider = "personParamExceptionTest", enabled = true)
    public void personParamExceptionTest(
            String caseName,
            IdNoTypeEnum idNoType,
            String idNo,
            String name,
            boolean encrypt,
            int errCode,
            String msg) {
        PersonParam personParam = new PersonParam();
        personParam.setIdNoType(idNoType);
        personParam.setIdNo(idNo);
        personParam.setName(name);
        personParam.setEncrypt(encrypt);
        AddAccountResult addAccountResult =
                serviceClient0000.accountService().addAccount(personParam);
        log.info(
                "请求参数{}，{}场景返回：{}",
                String.format("%s\t%s\t%s\t%s\t%s", caseName, idNoType, idNo, name, encrypt),
                caseName,
                addAccountResult.getMsg());
        Assert.assertEquals(addAccountResult.getErrCode(), errCode);
        Assert.assertTrue(addAccountResult.getMsg().contains(msg));
        Assert.assertEquals(addAccountResult.getErrCode(), errCode);
        Assert.assertTrue(addAccountResult.getMsg().contains(msg));
    }

    @DataProvider
    public Object[][] createTest() {
        return new Object[][] {
            {
                "未开启新版发证逻辑，创建、更新账号时触发制证",
                serviceClient0000,
                Params.getPersonParam(IdCard.getIdNo(), Names.getChineseName()),
                null,
                null,
                0
            },
        };
    }

    @Test(enabled = true)
    public void newVersionCreateCertOff() {
        log.info("正在执行：未开启新版发证逻辑，创建、更新账号时触发制证：");
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        String[] messages =
                Operations.checkCodeAndGetMessage(
                        project0000,
                        idNo,
                        phone,
                        name,
                        "realname_verification_code",
                        "验证码\\s*(\\d{6})");
        personParam.setIdentityAuthId(messages[0]);
        AddAccountResult addAccountResult =
                serviceClient0000.accountService().addAccount(personParam);
        log.info("不需要核身的流程传入核身id时无法创建账号：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 331002);
        Assert.assertTrue(addAccountResult.getMsg().contains("当前无法使用核身流程id创建账号"));
        personParam.setIdentityAuthId(null);
        String eviPointId =
                Operations.getEviPoint(
                                project0000,
                                UUID.randomUUID().toString(),
                                phone,
                                name,
                                idNo,
                                System.currentTimeMillis())
                        .getJSONObject("data")
                        .getString("eviPointId");
        personParam.setRealnameEvidencePointId(eviPointId);
        addAccountResult = serviceClient0000.accountService().addAccount(personParam);
        log.info("未开启新版发证逻辑返回：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 331002);
        Assert.assertTrue(addAccountResult.getMsg().contains("当前无法使用实名证据点创建账号"));
        personParam.setRealnameEvidencePointId("");
        addAccountResult = serviceClient0000.accountService().addAccount(personParam);
        log.info("正常创建用户，返回：{}", JSONObject.toJSONString(addAccountResult));
        String psnId = addAccountResult.getAccountId();
        Account accountInfoBeforeUpdate = Operations.getAccountInfo(psnId);
        log.info("创建的个人账号在用户中心的id为：{}", accountInfoBeforeUpdate.getId());
        Assert.assertEquals(accountInfoBeforeUpdate.getName(), personParam.getName());
        Assert.assertEquals(accountInfoBeforeUpdate.getIdCard(), personParam.getIdNo());
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(accountInfoBeforeUpdate.getId());
        log.info("创建的个人账号证书id为：{}", certInfoBeforeUpdate.getRaid());
        log.info("创建的个人账号证书持有人为：{}", certInfoBeforeUpdate.getCertName());
        Assert.assertEquals(certInfoBeforeUpdate.getCertName(), personParam.getName());
        log.info("开始更新个人账户：");
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(psnId);
        String newName = Names.getChineseName();
        updatePersonParam.setName(newName);
        serviceClient0000.accountService().updateAccount(updatePersonParam);
        log.info("通过accountId查询个人账号信息：");
        AccountInfoResult accountInfoResult =
                serviceClient0000.accountService().getAccountInfo(psnId, true);
        log.info("将{}用户，名字修改为{}", psnId, accountInfoResult.getName());
        Assert.assertEquals(accountInfoResult.getName(), newName);
        Account accountInfoAfterUpdate = Operations.getAccountInfo(psnId);
        log.info("更新后个人账号在用户中心的id不变：{}", accountInfoAfterUpdate.getId());
        Assert.assertEquals(accountInfoAfterUpdate.getId(), accountInfoBeforeUpdate.getId());
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(accountInfoAfterUpdate.getId());
        log.info("更新后个人账号证书id为：{}", certInfoAfterUpdate.getRaid());
        log.info("更新后个人账号证书持有人为：{}", certInfoAfterUpdate.getCertName());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), newName, "名字更新错误！");
        Assert.assertNotEquals(
                certInfoAfterUpdate.getRaid(), certInfoBeforeUpdate.getRaid(), "raid未更新！");
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn(), "sn未更新！");
    }

    private boolean checkBilling(
            String evidenceId, String evidenceName, String productId, String projectId) {
        Billings billings = new Billings();
        billings.setRequestBody(
                        Client.getTimevaleData().getOrgInfo(projectId).getGid() + "," + productId)
                .send();
        return billings.checkEvidence(evidenceId, evidenceName, projectId);
    }

    @Test(enabled = true)
    public void newVersionCreateCertOnWithoutWillingnessId() {
        log.info("正在执行：开启新版发证逻辑，不传核身id和实名证据点，无法创建或更新账号：");
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        personParam.setIdentityAuthId(null);
        AddAccountResult addAccountResult =
                serviceClient1000.accountService().addAccount(personParam);
        log.info("无identityAuthId不能创建账号：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 331002);
        Assert.assertTrue(addAccountResult.getMsg().contains("identityAuthId不能为空"));
        String evidenceName = UUID.randomUUID().toString();
        String eviPointId =
                Operations.getEviPoint(
                                project1000,
                                evidenceName,
                                phone,
                                name,
                                idNo,
                                System.currentTimeMillis())
                        .getJSONObject("data")
                        .getString("eviPointId");
        Assert.assertTrue(
                checkBilling(
                        eviPointId,
                        evidenceName,
                        Client.getTimevaleData().getEvidencePayId(),
                        Client.getTimevaleData().getNewVersionCreateCert().getId()));
        personParam.setRealnameEvidencePointId(eviPointId);
        addAccountResult = serviceClient1000.accountService().addAccount(personParam);
        log.info("不支持证据点，使用证据点创建账号，返回：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 331002);
        Assert.assertTrue(addAccountResult.getMsg().contains("identityAuthId不能为空"));
        String[] messages =
                Operations.checkCodeAndGetMessage(
                        project1000, idNo, phone, name, bizTemplateCode, "验证码\\s*(\\d{6})");
        String willingnessId = messages[0];
        personParam.setIdentityAuthId(willingnessId);
        personParam.setRealnameEvidencePointId(null);
        addAccountResult = serviceClient1000.accountService().addAccount(personParam);
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        Assert.assertTrue(addAccountResult.getMsg().contains("成功"));
        log.info("提交identityAuthId后可以创建账号：{}", JSONObject.toJSONString(addAccountResult));
        String psnId = addAccountResult.getAccountId();
        Account accountInfoBeforeUpdate = Operations.getAccountInfo(psnId);
        log.info("创建的个人账号在用户中心的id为：{}", accountInfoBeforeUpdate.getId());
        Assert.assertEquals(accountInfoBeforeUpdate.getName(), personParam.getName());
        Assert.assertEquals(accountInfoBeforeUpdate.getIdCard(), personParam.getIdNo());
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(accountInfoBeforeUpdate.getId());
        log.info("创建的个人账号证书id为：{}", certInfoBeforeUpdate.getRaid());
        log.info("创建的个人账号证书持有人为：{}", certInfoBeforeUpdate.getCertName());
        Assert.assertEquals(certInfoBeforeUpdate.getCertName(), personParam.getName());
        log.info("开始更新个人账户：");
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(psnId);
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setIdentityAuthId(null);
        Result result = serviceClient1000.accountService().updateAccount(updatePersonParam);
        log.info("新版发证未传核身信息或实名证据点无法更新账号信息：{}", JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 331002);
        Assert.assertTrue(result.getMsg().contains("identityAuthId不能为空"));
        eviPointId =
                Operations.getEviPoint(
                                project1000,
                                UUID.randomUUID().toString(),
                                phone,
                                name,
                                idNo,
                                System.currentTimeMillis())
                        .getJSONObject("data")
                        .getString("eviPointId");
        updatePersonParam.setRealnameEvidencePointId(eviPointId);
        result = serviceClient1000.accountService().updateAccount(updatePersonParam);
        log.info("不支持证据点，使用证据点更新账号，返回：{}", JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 331002);
        Assert.assertTrue(result.getMsg().contains("identityAuthId不能为空"));
        updatePersonParam.setIdentityAuthId(willingnessId);
        updatePersonParam.setRealnameEvidencePointId(null);
        result = serviceClient1000.accountService().updateAccount(updatePersonParam);
        log.info("传了{}的核身信息无法更新信息：{}", name, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 339005);
        Assert.assertTrue(result.getMsg().contains("核身流程中的账号指定的用户信息不一致"));
        messages =
                Operations.checkCodeAndGetMessage(
                        project1000, idNo, phone, newName, bizTemplateCode, "验证码\\s*(\\d{6})");
        willingnessId = messages[0];
        updatePersonParam.setIdentityAuthId(willingnessId);
        result = serviceClient1000.accountService().updateAccount(updatePersonParam);
        log.info("传了{}的核身信息可以更新信息：{}", newName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        Assert.assertTrue(result.getMsg().contains("成功"));
        log.info("通过accountId查询个人账号信息：");
        AccountInfoResult accountInfoResult =
                serviceClient1000.accountService().getAccountInfo(psnId, true);
        log.info("将{}用户，名字修改为{}", psnId, accountInfoResult.getName());
        Assert.assertEquals(accountInfoResult.getName(), newName);
        Account accountInfoAfterUpdate = Operations.getAccountInfo(psnId);
        log.info("更新后个人账号在用户中心的id不变：{}", accountInfoAfterUpdate.getId());
        Assert.assertEquals(accountInfoAfterUpdate.getId(), accountInfoBeforeUpdate.getId());
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(accountInfoAfterUpdate.getId());
        log.info("更新后个人账号证书id为：{}", certInfoAfterUpdate.getRaid());
        log.info("更新后个人账号证书持有人为：{}", certInfoAfterUpdate.getCertName());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), newName, "名字更新错误！");
        Assert.assertNotEquals(
                certInfoAfterUpdate.getRaid(), certInfoBeforeUpdate.getRaid(), "raid未更新！");
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn(), "sn未更新！");
    }

    @Test(enabled = true)
    public void newVersionCreateCertOnByEviPoint() {
        log.info("正在执行{}开启新版发证逻辑，使用证据点创建账号：", project1110.getId());
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        long timeStamp = System.currentTimeMillis();
        String eviPointId =
                Operations.getEviPoint(
                                project1110,
                                UUID.randomUUID().toString(),
                                phone,
                                name,
                                idNo,
                                timeStamp)
                        .getJSONObject("data")
                        .getString("eviPointId");
        PersonParam personParam = Params.getPersonParam(idNo, name);
        personParam.setIdentityAuthId(null);
        AddAccountResult addAccountResult =
                serviceClient1110.accountService().addAccount(personParam);
        log.info("无实名证据点和核身id不能创建账号：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 331002);
        Assert.assertTrue(addAccountResult.getMsg().contains("identityAuthId不能为空"));
        String errorEviPointId =
                Operations.getEviPoint(
                                project1110,
                                UUID.randomUUID().toString(),
                                phone,
                                name,
                                IdCard.getIdNo(),
                                timeStamp)
                        .getJSONObject("data")
                        .getString("eviPointId");
        personParam.setRealnameEvidencePointId(errorEviPointId);
        addAccountResult = serviceClient1110.accountService().addAccount(personParam);
        log.info("证据点信息不匹配，返回：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 339004);
        Assert.assertTrue(addAccountResult.getMsg().contains("证据点中和账号指定的用户信息不一致"));
        personParam.setRealnameEvidencePointId(eviPointId);
        addAccountResult = serviceClient1110.accountService().addAccount(personParam);
        log.info("提交实名证据点后可以创建账号：{}", JSONObject.toJSONString(addAccountResult));
        String psnId = addAccountResult.getAccountId();
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        personSignParam.setSealData(Client.getTimevaleData().getSealData());
        personSignParam.setAccountId(psnId);
        String[] messages =
                Operations.checkCodeAndGetMessage(
                        project1110,
                        idNo,
                        phone,
                        name,
                        "realname_verification_code",
                        "验证码\\s*(\\d{6})");
        personSignParam.setWillingnessId(messages[0]);
        FileDigestSignResult fileDigestSignResult =
                serviceClient1110.userSignService().personSign(personSignParam);
        log.info("签署成功，返回：{}", fileDigestSignResult.getMsg());
        Operations.checkEvi(
                project1110, serviceClient1110, fileDigestSignResult.getDstFilePath(), name);
        Account accountInfoBeforeUpdate = Operations.getAccountInfo(psnId);
        log.info("创建的个人账号在用户中心的id为：{}", accountInfoBeforeUpdate.getId());
        Assert.assertEquals(accountInfoBeforeUpdate.getName(), personParam.getName());
        Assert.assertEquals(accountInfoBeforeUpdate.getIdCard(), personParam.getIdNo());
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(accountInfoBeforeUpdate.getId());
        log.info("创建的个人账号证书id为：{}", certInfoBeforeUpdate.getRaid());
        log.info("创建的个人账号证书持有人为：{}", certInfoBeforeUpdate.getCertName());
        Assert.assertEquals(certInfoBeforeUpdate.getCertName(), personParam.getName());
        log.info("开始更新个人账户：");
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(psnId);
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setIdentityAuthId(null);
        Result result = serviceClient1110.accountService().updateAccount(updatePersonParam);
        log.info("新版发证未传核身信息或实名证据点无法更新账号信息：{}", JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 331002);
        Assert.assertTrue(result.getMsg().contains("identityAuthId不能为空"));
        updatePersonParam.setRealnameEvidencePointId(eviPointId);
        result = serviceClient1110.accountService().updateAccount(updatePersonParam);
        log.info("传了{}的核身信息无法更新信息：{}", name, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 339004);
        Assert.assertTrue(result.getMsg().contains("证据点中和账号指定的用户信息不一致"));
        eviPointId =
                Operations.getEviPoint(
                                project1110,
                                UUID.randomUUID().toString(),
                                phone,
                                newName,
                                idNo,
                                timeStamp)
                        .getJSONObject("data")
                        .getString("eviPointId");
        updatePersonParam.setRealnameEvidencePointId(eviPointId);
        result = serviceClient1110.accountService().updateAccount(updatePersonParam);
        log.info("传了{}的核身信息可以更新信息：{}", newName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        Assert.assertTrue(result.getMsg().contains("成功"));
        log.info("通过accountId查询个人账号信息：");
        AccountInfoResult accountInfoResult =
                serviceClient1110.accountService().getAccountInfo(psnId, true);
        log.info("将{}用户，名字修改为{}", psnId, accountInfoResult.getName());
        Assert.assertEquals(accountInfoResult.getName(), newName);
        Account accountInfoAfterUpdate = Operations.getAccountInfo(psnId);
        log.info("更新后个人账号在用户中心的id不变：{}", accountInfoAfterUpdate.getId());
        Assert.assertEquals(accountInfoAfterUpdate.getId(), accountInfoBeforeUpdate.getId());
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(accountInfoAfterUpdate.getId());
        log.info("更新后个人账号证书id为：{}", certInfoAfterUpdate.getRaid());
        log.info("更新后个人账号证书持有人为：{}", certInfoAfterUpdate.getCertName());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), newName, "名字更新错误！");
        Assert.assertNotEquals(
                certInfoAfterUpdate.getRaid(), certInfoBeforeUpdate.getRaid(), "raid未更新！");
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn(), "sn未更新！");
    }

    @Test(enabled = true)
    public void newVersionCreateCertOnByEviPointAndWillingnessId() {
        log.info("正在执行：同时使用新版核身id和实名证据点，取核身信息：");
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        long timeStamp = System.currentTimeMillis();
        String eviPointId =
                Operations.getEviPoint(
                                project1110,
                                UUID.randomUUID().toString(),
                                phone,
                                name,
                                IdCard.getIdNo(),
                                timeStamp)
                        .getJSONObject("data")
                        .getString("eviPointId");
        PersonParam personParam = Params.getPersonParam(idNo, name);
        String[] messages =
                Operations.checkCodeAndGetMessage(
                        project1110, idNo, phone, name, bizTemplateCode, "验证码\\s*(\\d{6})");
        Assert.assertTrue(messages[1].contains("进行实名认证并申领数字证书，回填验证码即代表同意《数字证书协议》"));
        String willingnessId = messages[0];
        personParam.setIdentityAuthId(willingnessId);
        personParam.setRealnameEvidencePointId(eviPointId);
        AddAccountResult addAccountResult =
                serviceClient1110.accountService().addAccount(personParam);
        log.info(
                "同时传入identityAuthId与realnameEvidencePointId时不能创建账号：{}",
                JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 20001);
        Assert.assertTrue(
                addAccountResult.getMsg().contains("identityAuthId与realnameEvidencePointId不能同时存在"));
        personParam.setRealnameEvidencePointId(null);
        addAccountResult = serviceClient1110.accountService().addAccount(personParam);
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        Assert.assertTrue(addAccountResult.getMsg().contains("成功"));
        log.info("创建成功，返回：{}", JSONObject.toJSONString(addAccountResult));
        String psnId = addAccountResult.getAccountId();
        Account accountInfoBeforeUpdate = Operations.getAccountInfo(psnId);
        log.info("创建的个人账号在用户中心的id为：{}", accountInfoBeforeUpdate.getId());
        Assert.assertEquals(accountInfoBeforeUpdate.getName(), personParam.getName());
        Assert.assertEquals(accountInfoBeforeUpdate.getIdCard(), personParam.getIdNo());
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(accountInfoBeforeUpdate.getId());
        log.info("创建的个人账号证书id为：{}", certInfoBeforeUpdate.getRaid());
        log.info("创建的个人账号证书持有人为：{}", certInfoBeforeUpdate.getCertName());
        Assert.assertEquals(certInfoBeforeUpdate.getCertName(), personParam.getName());
        log.info("开始更新个人账户：");
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(psnId);
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        messages =
                Operations.checkCodeAndGetMessage(
                        project1110, idNo, phone, newName, bizTemplateCode, "验证码\\s*(\\d{6})");
        updatePersonParam.setIdentityAuthId(messages[1]);
        updatePersonParam.setRealnameEvidencePointId(eviPointId);
        Result result = serviceClient1110.accountService().updateAccount(updatePersonParam);
        log.info(
                "同时传入identityAuthId与realnameEvidencePointId时不能更新账号：{}",
                JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 20001);
        Assert.assertTrue(result.getMsg().contains("identityAuthId与realnameEvidencePointId不能同时存在"));
        updatePersonParam.setIdentityAuthId("");
        eviPointId =
                Operations.getEviPoint(
                                project1110,
                                UUID.randomUUID().toString(),
                                phone,
                                newName,
                                idNo,
                                timeStamp)
                        .getJSONObject("data")
                        .getString("eviPointId");
        updatePersonParam.setRealnameEvidencePointId(eviPointId);
        result = serviceClient1110.accountService().updateAccount(updatePersonParam);
        log.info("更新账号信息返回：{}", JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        Assert.assertTrue(result.getMsg().contains("成功"));
        log.info("通过accountId查询个人账号信息：");
        AccountInfoResult accountInfoResult =
                serviceClient1110.accountService().getAccountInfo(psnId, true);
        log.info("将{}用户，名字修改为{}", psnId, accountInfoResult.getName());
        Assert.assertEquals(accountInfoResult.getName(), newName);
        Account accountInfoAfterUpdate = Operations.getAccountInfo(psnId);
        log.info("更新后个人账号在用户中心的id不变：{}", accountInfoAfterUpdate.getId());
        Assert.assertEquals(accountInfoAfterUpdate.getId(), accountInfoBeforeUpdate.getId());
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(accountInfoAfterUpdate.getId());
        log.info("更新后个人账号证书id为：{}", certInfoAfterUpdate.getRaid());
        log.info("更新后个人账号证书持有人为：{}", certInfoAfterUpdate.getCertName());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), newName, "名字更新错误！");
        Assert.assertNotEquals(
                certInfoAfterUpdate.getRaid(), certInfoBeforeUpdate.getRaid(), "raid未更新！");
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn(), "sn未更新！");
    }
}
