package test.cert;

import com.alibaba.fastjson2.JSONObject;
import com.api.rpc.easun_service.updateDetail.Input;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.OrgSignParam;
import com.timevale.esign.paas.tech.bean.request.OrganizeParam;
import com.timevale.esign.paas.tech.bean.request.PersonParam;
import com.timevale.esign.paas.tech.bean.request.QueryAccountInfoParam;
import com.timevale.esign.paas.tech.bean.request.UpdateOrganizeParam;
import com.timevale.esign.paas.tech.bean.result.AddAccountResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.Result;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;
import com.util.data.param.Params;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class OrgCertTest {
    private String env;
    private Project app0000;
    private ServiceClient serviceClient0000;
    private Project app1110;
    private ServiceClient serviceClient1110;
    private Project app0010;
    private ServiceClient serviceClient0010;
    private Project app0011;
    private ServiceClient serviceClient0011;
    private Project appWeakAuth;
    private ServiceClient serviceClientWeakAuth;
    private Project app1000;
    private ServiceClient serviceClient1000;
    private Project app1001;
    private ServiceClient serviceClient1001;
    private Project app1010;
    private ServiceClient serviceClient1010;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String OUTPUT = "./output/222.pdf";
    private String psnIdNo;
    private String psnName;
    private String phone = "18610049949";
    private String bizTemplateCode = "realname_verification_code";
    private final String oldOrgName = "esigntest测试用户经营的个体工商户";
    private final String orgCode = "91000000274202174K";
    private final String orgName = "esigntest快捷签专用测试企业";

    private String psnId0000;
    private String orgId0000;
    private String orgId1000;
    private String orgId1001;
    private String orgSealData;

    @BeforeClass
    @Parameters({"env"})
    private void init(String env) {
        this.env = env;
        Client.setTimevaleData(env);
        app0000 = Client.getTimevaleData().getProject();
        app1110 =
                Client.getTimevaleData()
                        .getNewVersionCreateCertAndRealnameEvidencePointAndOfflineAuthTransactorWill();
        app0010 = Client.getTimevaleData().getOfflineAuthTransactorWill();
        app0011 = Client.getTimevaleData().getStrongAuthAndOfflineAuthTransactorWill();
        appWeakAuth = Client.getTimevaleData().getWeakAuth();
        app1000 = Client.getTimevaleData().getNewVersionCreateCert();
        app1001 = Client.getTimevaleData().getNewVersionCreateCertAndWeakAuth();
        app1010 = Client.getTimevaleData().getNewVersionCreateCertAndOfflineAuthTransactorWill();
        serviceClient0000 = Client.getClient(env, app0000);
        serviceClient1110 = Client.getClient(env, app1110);
        serviceClient1000 = Client.getClient(env, app1000);
        serviceClient1001 = Client.getClient(env, app1001);
        psnIdNo = IdCard.getIdNo();
        psnName = "测试" + Names.getChineseName(psnIdNo);
        PersonParam personParam = Params.getPersonParam(psnIdNo, psnName);
        String[] message =
                Operations.checkCodeAndGetMessage(
                        app0000, psnIdNo, phone, psnName, bizTemplateCode, "验证码\\s*(\\d{6})");
        String willingnessId = message[1];
        Assert.assertFalse(message[0].contains("进行实名认证并申领数字证书，回填验证码即代表同意《数字证书协议》"));
        //        personParam.setIdentityAuthId(willingnessId);
        AddAccountResult addAccountResult =
                serviceClient0000.accountService().addAccount(personParam);
        log.info("在0000中创建个人账号返回：{}", JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        psnId0000 = addAccountResult.getAccountId();
        orgSealData = Params.getOrgSealData(serviceClient1000, orgName);
    }

    @Test(priority = 1, enabled = true)
    public void oldCreateCertByCreateAccount() {
        log.info("开始创建企业账户：");
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, oldOrgName);
        AddAccountResult addAccountResult =
                serviceClient0000.accountService().addAccount(organizeParam);
        orgId0000 = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", orgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(orgId0000);
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        String sn = certInfo.getSn();
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("老版发证逻辑，在创建账号时发证：{}", sn);
        Operations.offlineAuth(serviceClient0000, 1, orgId0000, psnId0000);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("签署授权书后sn未更新：{}", certInfo.getSn());
        Assert.assertEquals(certInfo.getSn(), sn);
    }

    @Test(
            priority = 2,
            dependsOnMethods = {"oldCreateCertByCreateAccount"},
            enabled = true)
    public void oldUpdateCertByUpdateAccount() {
        Account account = Operations.getAccountInfo(orgId0000);
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(account.getId());
        log.info("更新前的sn为：{}", certInfoBeforeUpdate.getSn());
        log.info("开始更新企业账户：");
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(orgId0000);
        updateOrganizeParam.setName(orgName);
        Result result = serviceClient0000.accountService().updateAccount(updateOrganizeParam);
        log.info("更新账号成功{}，返回：{}", orgName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), orgName);
        log.info("老版发证逻辑，在创建账号时发证：{}", certInfoAfterUpdate.getSn());
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn());
    }

    @Test(priority = 3, enabled = true)
    public void newCreateCertAndOrgAuthReplaceModeByCreateAccount() {
        log.info("开始创建企业账户：");
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, oldOrgName);
        AddAccountResult addAccountResult =
                serviceClient1001.accountService().addAccount(organizeParam);
        orgId1001 = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", orgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        Account account = Operations.getAccountInfo(orgId1001);
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("老版发证逻辑，在创建账号时发证：{}", certInfo.getSn());
        Operations.offlineAuth(serviceClient1001, 1, orgId1001, null);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("签署授权书后sn未更新：{}", certInfo.getSn());
    }

    @Test(
            priority = 4,
            dependsOnMethods = {"newCreateCertAndOrgAuthReplaceModeByCreateAccount"},
            enabled = true)
    public void newCreateCertAndOrgAuthReplaceModeByUpdateAccount() {
        Account account = Operations.getAccountInfo(orgId1001);
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(account.getId());
        log.info("更新前的sn为：{}", certInfoBeforeUpdate.getSn());
        log.info("开始更新企业账户：");
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(orgId1001);
        updateOrganizeParam.setName(orgName);
        Result result = serviceClient1001.accountService().updateAccount(updateOrganizeParam);
        log.info("更新账号成功{}，返回：{}", orgName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), orgName);
        log.info("老版发证逻辑，在创建账号时发证：{}", certInfoAfterUpdate.getSn());
        Assert.assertNotEquals(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn());
        clearAccounts(serviceClient1001);
    }

    @Test(priority = 5, enabled = true)
    public void newCreateCertAndNotOrgAuthReplaceModeByCreateAccountOfflineAuth() {
        clearAccounts(serviceClient1000);
        log.info("开始创建企业账户：");
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, oldOrgName);
        AddAccountResult addAccountResult =
                serviceClient1000.accountService().addAccount(organizeParam);
        orgId1000 = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", oldOrgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        Account account = Operations.getAccountInfo(orgId1000);
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        log.info("新版发证逻辑，在创建账号时不发证");
        Assert.assertNull(certInfo);
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId1000);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("授权校验先于证书校验，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
        String offlineAuthId = Operations.offlineAuth(serviceClient1000, 1, orgId1000, null);
        log.info("同意授权：");
        Operations.auditing(app1000, offlineAuthId, "通过", "同意授权!");
        log.info("等待审批状态同步：");
        Operations.checkAuthStatus(serviceClient1000, 2, offlineAuthId);
        Operations.sleep(5_000);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("签署授权书后成功申领证书：{}", certInfo.getSn());
        fileDigestSignResult = serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("发证后签署成功，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Operations.verifyPdf(app1000, serviceClient1000, OUTPUT, oldOrgName);
    }

    @Test(
            priority = 6,
            dependsOnMethods = {"newCreateCertAndNotOrgAuthReplaceModeByCreateAccountOfflineAuth"},
            enabled = true)
    public void newCreateCertAndNotOrgAuthReplaceModeByUpdateAccountOfflineAuth() {
        Account account = Operations.getAccountInfo(orgId1000);
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(account.getId());
        log.info("更新前的sn为：{}", certInfoBeforeUpdate.getSn());
        log.info("开始更新企业账户：");
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(orgId1000);
        updateOrganizeParam.setName(orgName);
        Result result = serviceClient1000.accountService().updateAccount(updateOrganizeParam);
        log.info("更新账号成功{}，返回：{}", orgName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), orgName);
        log.info("新版发证逻辑，在更新账号时重新制证：{}", certInfoAfterUpdate.getCertName());
        log.info(certInfoAfterUpdate.getSn());
        log.info(certInfoBeforeUpdate.getSn());
        Assert.assertNotNull(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn());
        Assert.assertNotNull(certInfoAfterUpdate.getCertName(), certInfoBeforeUpdate.getCertName());
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId1000);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("发证后签署成功，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Operations.verifyPdf(app1000, serviceClient1000, OUTPUT, orgName);
        clearAccounts(serviceClient1000);
    }

    @Test(priority = 7, enabled = true)
    public void newCreateCertAndNotOrgAuthReplaceModeByCreateAccountOnlineAuth() {
        log.info("开始创建企业账户：");
        Input input = new Input();
        log.info("初始化，将企业名从{}更新为{}", orgName, oldOrgName);
        String oid = Client.getTimevaleData().getOrgInfoByCode(orgCode).getOuid();
        input.setRequestBody(oid + "," + oldOrgName).send();
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, oldOrgName);
        AddAccountResult addAccountResult =
                serviceClient1000.accountService().addAccount(organizeParam);
        orgId1000 = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", oldOrgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        Account account = Operations.getAccountInfo(orgId1000);
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        log.info("新版发证逻辑，在创建账号时不发证");
        Assert.assertNull(certInfo);
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId1000);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("授权校验先于证书校验，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
        String psnIdNo = "421300199611128676";
        String psnMobile = "***********";
        String psnName = "测试连宏";
        PersonParam personParam = Params.getPersonParam(psnIdNo, psnName);
        addAccountResult = serviceClient1000.accountService().addAccount(personParam);
        log.info("授权校验先于证书校验，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
        String psnId1000 = addAccountResult.getAccountId();
        Operations.sleep(5_000);
        Operations.onlineAuth(serviceClient1000, 1, psnMobile, orgId1000, psnId1000);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfo.getCertName(), oldOrgName);
        log.info("签署授权书后成功申领证书：{}", certInfo.getSn());
        fileDigestSignResult = serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("发证后签署成功，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Operations.verifyPdf(app1000, serviceClient1000, OUTPUT, oldOrgName);
        log.info("将企业名从{}还原为{}", oldOrgName, orgName);
        input.setRequestBody(oid + "," + orgName).send();
    }

    @Test(
            priority = 8,
            dependsOnMethods = {"newCreateCertAndNotOrgAuthReplaceModeByCreateAccountOnlineAuth"})
    public void newCreateCertAndNotOrgAuthReplaceModeByUpdateAccountOnlineAuth() {
        Account account = Operations.getAccountInfo(orgId1000);
        CertInfo certInfoBeforeUpdate = Operations.getCertInfo(account.getId());
        log.info("更新前的sn为：{}", certInfoBeforeUpdate.getSn());
        log.info("开始更新企业账户：");
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(orgId1000);
        updateOrganizeParam.setName(orgName);
        Result result = serviceClient1000.accountService().updateAccount(updateOrganizeParam);
        log.info("更新账号成功{}，返回：{}", orgName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        CertInfo certInfoAfterUpdate = Operations.getCertInfo(account.getId());
        Assert.assertEquals(certInfoAfterUpdate.getCertName(), orgName);
        log.info("新版发证逻辑，在更新账号时重新制证：{}", certInfoAfterUpdate.getCertName());
        log.info(certInfoAfterUpdate.getSn());
        log.info(certInfoBeforeUpdate.getSn());
        Assert.assertNotNull(certInfoAfterUpdate.getSn(), certInfoBeforeUpdate.getSn());
        Assert.assertNotNull(certInfoAfterUpdate.getCertName(), certInfoBeforeUpdate.getCertName());
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId1000);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient1000.userSignService().orgSign(orgSignParam);
        log.info("发证后签署成功，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Operations.verifyPdf(app1000, serviceClient1000, OUTPUT, orgName);
        clearAccounts(serviceClient1000);
    }

    private void clearAccounts(ServiceClient serviceClient) {
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    @AfterClass
    public void clear() {
        clearAccounts(serviceClient0000);
    }
}
