package test;

import com.entity.esign.Project;
import com.util.Client;
import com.util.file.FileUtil;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.Parameters;

@Slf4j
public class RegisterTest {

    //    @Parameters({"env"})
    //    @BeforeSuite
    //    public void register(String env) {
    //        Client.clear();
    //        log.info("正在{}进行注册", env);
    //        FileUtil.forceDelete("output");
    //        Client.setTimevaleData(env);
    //        Assert.assertNotNull(Client.getClient(env).accountService());
    //        //        Client.getClient(
    //        //                env,
    //        //                Client.getTimevaleData().getProjectIdOnStrongAuth(),
    //        //                Client.getTimevaleData().getProjectSecretOnStrongAuth());
    //    }

    @Parameters({"env"})
    @BeforeSuite
    public void register(String env, String projectId, String projectSecret) {
        log.info("正在{}进行注册", env);
        FileUtil.forceDelete("output");
        Client.setTimevaleData(env);
        Assert.assertNotNull(
                Client.getClient(env, new Project(projectId, projectSecret)).accountService());
        Client.getClient(env, new Project(projectId, projectSecret));
    }
}
