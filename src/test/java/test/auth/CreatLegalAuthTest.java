package test.auth;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.IdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.OrganRegTypeEnum;
import com.util.Client;
import com.util.data.param.Params;
import com.util.file.FileUtil;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.*;

import java.io.IOException;
import java.util.Arrays;

// 法人静默签授权（授权至平台/经办人，线上模式）
@Slf4j
public class CreatLegalAuthTest {
    final boolean enabled = true;

    private static final int AUTH_TYPE_PLATFORM = 1; // 授权给平台
    private static final int AUTH_TYPE_PSN = 2; // 授权给经办人
    private static final int PSN_USE_LEGAL_CERT_SIGN = 1; // 平台使用法人证书签署
    private static final int PLATFORM_USE_LEGAL_CERT_SIGN = 2; // 经办人使用法人证书签署
    private String orgAccountId;
    private String orgAccountId_2;
    private ServiceClient serviceClient;
    private String personAccountId;

    private Project project;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String OUTPUT = "./output/11111.pdf";
    //    private static final String OUTPUT_2 = "./output/经办人_签署完的文件.pdf";
    private byte[] fileStream;
    private String willingnessId; // ="3871757808601400800";
    private String authId_toPsn;
    private String authId_toPlatform;
    private String orgSealData;

    private AddAccountResult accountInfoResult_org;
    private AddAccountResult accountInfoResult_org_2;
    private AddAccountResult accountInfoResult_psn;
    private String notifyUrl = Operations.getCALL_BACK();
    private String redirectUrl = "http://www.baidu.com";

    private AuthFlowSignUrlParam urlParam;

    private String psnMobile = "***********"; // 企业经办人账号
    private String psnName = "测试猫猫零"; // 企业经办人账号
    private String psnIdNo = "432427189712163920"; // 企业经办人账号

    // //法人账号信息
    private String legalMobile = "***********";
    private String legalIdNo = "*****************3";
    private String legalName = "彭蛋花";
    private String orgIdNo = "9100000066135592GF";
    private String orgName = "esigntest彭蛋花企业";

    // 线上账号信息
    //    private final String psnIdNo = "430522199605058069";
    //    private final String psnName = "彭丹";
    //    private final String psnMobile = "***********";
    //
    //    private final String orgIdNo = "91310117MA1J5D3R9Q";
    //    private final String legalName = "杨阔";
    //    private final String legalIdNo = "211202199509211754";
    //    private final String legalMobile = "***********";
    //    private final String orgName = "上海灵契软件中心";

    private String orgName_2 = "彭蛋花企业";
    private String orgIdNo_2 = "9100000004975632UX";

    @BeforeClass
    @Parameters({"env"})
    private void init(String env) throws IOException {
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getProject();
        serviceClient = Client.getClient(env, project);
        // 预先清除数据
        clearAccounts();
        Operations.mock(Arrays.asList(legalMobile, psnMobile));
        fileStream = FileUtil.getFileStream(FILE_PATH);
        urlParam = new AuthFlowSignUrlParam();
        // 创建一个企业账号，法人授权使用
        OrganizeParam organizeParam = new OrganizeParam();
        organizeParam.setOrgCode(orgIdNo);
        organizeParam.setRegType(OrganRegTypeEnum.MERGE);
        organizeParam.setName(orgName);
        accountInfoResult_org = serviceClient.accountService().addAccount(organizeParam);
        orgAccountId = accountInfoResult_org.getAccountId();
        orgSealData = Params.getOrgSealData(serviceClient, orgName);
        // 创建企业账号2，测试使用
        organizeParam.setName(orgName_2);
        organizeParam.setOrgCode(orgIdNo_2);
        accountInfoResult_org_2 = serviceClient.accountService().addAccount(organizeParam);
        orgAccountId_2 = accountInfoResult_org_2.getAccountId();
        // 创建经办人账号
        PersonParam personParam = new PersonParam();
        personParam.setIdNo(psnIdNo);
        personParam.setName(psnName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        accountInfoResult_psn = serviceClient.accountService().addAccount(personParam);
        personAccountId = accountInfoResult_psn.getAccountId();
    }

    @DataProvider
    public Object[][] createAuthDataRight() {
        int authType = 1;
        String contact = "***********";
        boolean sendNotice = false;
        String sealScope = "321321";
        String fileType = "321321";
        long validDate = System.currentTimeMillis() + 24 * 3600 * 1000;
        String notifyUrl = Operations.getCALL_BACK();
        String redirectUrl = "https://www.baidu.com";
        return new Object[][] {
            {
                "正确格式场景",
                orgAccountId,
                "",
                authType,
                contact,
                sendNotice,
                sealScope,
                fileType,
                validDate,
                notifyUrl,
                redirectUrl,
                0,
                "321321"
            },
        };
    }

    @DataProvider
    public Object[][] createAuthDataException() {
        int authType = 1;
        String name = "测试猫猫零";
        String contact = "***********";
        String idNo = "432427189712163920";
        boolean encrypt = false;
        boolean sendNotice = false;
        String sealScope = "合同专用章、人事专用章";
        String fileType = "贷款协议、运输协议、物流条款";

        // organizeId, name, idNoType, idNo, encrypt, contact, sendNotice, authType, personId,
        // sealScope, fileType, notifyUrl, redirectUrl
        return new Object[][] {
            {
                "organizeId为空", // caseNmae
                "", // organizeId
                "测试猫猫零", // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                authType,
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "organizeId不能为空"
            },
            {
                "传个人oid", // caseName
                personAccountId, // organizeId
                name, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331003,
                "账号信息异常或不存在"
            },
            {
                "不存在的企业ID", // caseNmae
                "************", // organizeId
                name, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331003,
                "账号信息异常或不存在"
            },
            {
                "name为空", // caseNmae
                orgAccountId, // organizeId
                "", // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "name不能为空"
            },
            {
                "idNo为空", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                "", // idNo
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "idNo不能为空"
            },
            {
                "idNoType为空", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                null, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "idNoType不能为空"
            },
            {
                "contact为空", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo为空
                encrypt,
                "",
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "contact不能为空"
            },
            {
                "contact-手机格式错误", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo,
                encrypt,
                "**********",
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "contact联系方式格式错误"
            },
            {
                "contact-邮箱格式错误", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo,
                encrypt,
                "1224qq.com",
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "contact联系方式格式错误"
            },
            // 证件号格式校验
            {
                "证件号格式校验-大陆身份证", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.MAINLAND, // idNoType
                "*****************", // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "证件号格式校验-香港", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.HONGKONG, // idNoType
                "**********", // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "证件号格式校验-澳门", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.HONGKONG, // idNoType
                "**********", // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "证件号格式校验-台湾", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.TAIWAN, // idNoType
                "T**********", // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331002,
                "个人证件号与证件类型不匹配"
            },
            {
                "证件号格式校验-护照", // caseNmae
                orgAccountId, // organizeId
                name, // name 为空
                IdNoTypeEnum.PASSPORT, // idNoType
                "12345678901234567890123456", // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331002,
                "个人证件号与证件类型不匹配"
            },
            // 证件号格式校验
            {
                "企业四要素校验-比对不通过", // caseNmae
                orgAccountId_2, // organizeId
                name,
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // 错误的证件号
                encrypt,
                contact,
                sendNotice,
                authType, // authType
                personAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                339002,
                "信息对比查询失败"
            },
            {
                "授权给经办人账号-不传经办人oid", // caseNmae
                orgAccountId, // organizeId
                name, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                2, // authType
                "",
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                10001,
                "授权至经办人时，个人账号不能为空"
            },
            {
                "授权给经办人账号-经办人账号不存在", // caseNmae
                orgAccountId, // organizeId
                name, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                2, // authType
                "********",
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331003,
                "账号信息异常或不存在"
            },
            {
                "授权给经办人账号-经办人账号传企业账号ID", // caseNmae
                orgAccountId, // organizeId
                name, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                idNo, // idNo
                encrypt,
                contact,
                sendNotice,
                2, // authType
                orgAccountId,
                sealScope,
                fileType,
                notifyUrl,
                redirectUrl,
                331003,
                "账号信息异常或不存在"
            },
        };
    }

    @Test(dataProvider = "createAuthDataException", enabled = true)
    public void createLegalRepExceptTest(
            String caseName,
            String organizeId,
            String name,
            IdNoTypeEnum idNoType,
            String idNo,
            Boolean encrypt,
            String contact,
            boolean sendNotice,
            int authType,
            String personId,
            String sealScope,
            String fileType,
            String notifyUrl,
            String redirectUrl,
            int errCode,
            String msg) {

        OnlineCreateLegalRepAuthParam authParam =
                buildOnlineCreateLegalRepAuthParam(
                        organizeId,
                        name,
                        idNoType,
                        idNo,
                        encrypt,
                        contact,
                        sendNotice,
                        authType,
                        personId,
                        sealScope,
                        fileType,
                        notifyUrl,
                        redirectUrl);
        OnlineCreateAuthResult Result = serviceClient.authService().createLegalAuth(authParam);
        //        log.info("{}请求参数：{}", caseName, JSONObject.toJSONString(authParam));
        log.info("{}场景返回：{}", caseName, JSONObject.toJSONString(Result));
        Assert.assertEquals(Result.getErrCode(), errCode);
        if (Result.getErrCode() != 0) {
            Assert.assertTrue(Result.getMsg().contains(msg));
        }
    }

    /** 法人授权平台_测试场景 */
    @Test(enabled = enabled)
    public void createLegalAuthTest() {
        String legalRepId;
        String scope = "天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！";
        // 1：发起授权：授权给平台
        OnlineCreateLegalRepAuthParam authParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName,
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        false,
                        legalMobile,
                        true,
                        AUTH_TYPE_PLATFORM,
                        null,
                        scope,
                        scope,
                        notifyUrl,
                        redirectUrl);
        log.info("发起法人授权平台签署：{}", JSONObject.toJSONString(authParam));
        OnlineCreateAuthResult legalAuthResult =
                serviceClient.authService().createLegalAuth(authParam);
        Assert.assertEquals(legalAuthResult.getErrCode(), 0);
        authId_toPlatform = legalAuthResult.getAuthId();
        log.info("发起法人授权平台签署,authId_toPlatform:{}", authId_toPlatform);
        // 2：根据authId ，查询授权信息
        AuthInfoResult authInfoResult =
                Operations.checkAuthStatus(serviceClient, 1, authId_toPlatform);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2);
        Assert.assertNotNull(authInfoResult.getLegalRepId());
        legalRepId = authInfoResult.getLegalRepId();
        log.info("查询授权记录信息：{}", JSONObject.toJSONString(authInfoResult));

        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(legalRepId);
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNull(certInfo, "发起法人授权签署-创建法人账号-查询法人证书信息为空");

        // 3： 获取授权链接
        urlParam.setAuthId(authId_toPlatform);
        AuthFlowSignUrlResult urlResult = serviceClient.authService().getAuthFlowUrl(urlParam);
        log.info("获取授权签署链接：{}", JSONObject.toJSONString(urlResult));

        // 4  完成授权书签署
        Operations.executionAuthSignFlow(serviceClient, legalAuthResult.getAuthId());
        // 断点处：需要签署授权书
        // 5：查询授权信息-已授权完成
        authInfoResult = Operations.checkAuthStatus(serviceClient, 2, authId_toPlatform);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2);
        log.info("授权书签署完成后，查询授权记录-授权状态：{}", authInfoResult.getStatus().toString());

        // 授权完成后，给法人制证
        //        Query.setNewDatabase(false);
        account = Operations.getAccountInfo(authInfoResult.getLegalRepId());
        log.info(
                "查询法人账号：【{}】信息,返回{}",
                authInfoResult.getLegalRepId(),
                JSONObject.toJSONString(account));
        certInfo = Operations.getCertInfo(account.getId());
        log.info("查询法人账号：【{}】证书信息,返回{}", account.getId(), JSONObject.toJSONString(certInfo));

        Assert.assertNotNull(certInfo, "签完法人授权书后未制证");
        Assert.assertEquals(certInfo.getCertName(), legalName);

        log.info("创建的法人账号证书为：{}", JSONObject.toJSONString(certInfo));

        //  法人已授权平台，使用法人证书-可使用平台代法人签署
        FileDigestSignResult fileDigestSignResult = useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        log.info(
                "法人{}授权平台签署，平台可使用法人证书签署:{}",
                legalName,
                JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        Operations.checkEvi(
                project, serviceClient, OUTPUT, legalName + ",授权流程id：" + authId_toPlatform);
        clear();

        // 法人已授权平台， 经办人无授权，使用法人证书进行企业签署，可签署成功
        FileDigestSignResult fileDigestSignResult_2 = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info(
                "法人{}授权平台签署，经办人未授权，可使用法人证书签署:{}",
                legalName,
                JSONObject.toJSONString(fileDigestSignResult_2));
        Assert.assertEquals(fileDigestSignResult_2.getErrCode(), 0);
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        Operations.checkEvi(
                project, serviceClient, OUTPUT, legalName + ",授权流程id：" + authId_toPlatform);
        clear();

        // 未授权企业签署，使用企业签
        FileDigestSignResult fileDigestSignResult_org =
                useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN, true);
        log.info(
                "法人{}授权平台签署，企业未授权平台，使用企业证书签署,:{}",
                legalName,
                JSONObject.toJSONString(fileDigestSignResult_org));
        Assert.assertEquals(
                fileDigestSignResult_org.getErrCode(), 335001, "平台有法人授权无企业授权，使用企业证书签署成功");

        // 未授权企业签署，使用经办人签
        FileDigestSignResult fileDigestSignResult_psn =
                useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN, true);
        Assert.assertEquals(
                fileDigestSignResult_psn.getErrCode(), 335001, "平台有法人授权无企业授权，经办人使用企业证书签署成功");

        // 取消授权
        serviceClient.authService().cancel(authId_toPlatform);
        // 2：根据authId ，查询授权信息
        authInfoResult = Operations.checkAuthStatus(serviceClient, 4, authId_toPlatform);
        log.info("查询授权记录：{}", JSONObject.toJSONString(authInfoResult));

        //  法人授权平台已取消，平台使用法人证书签署，签署失败
        FileDigestSignResult fileDigestSignResult_3 =
                useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        log.info(
                "法人{}授权平台签署已取消，平台使用法人证书签署失败:{}",
                legalName,
                JSONObject.toJSONString(fileDigestSignResult_3));
        Assert.assertEquals(fileDigestSignResult_3.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult_3.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 法人授权平台已取消， 经办人无授权，使用法人证书进行企业签署，签署失败
        FileDigestSignResult fileDigestSignResult_4 = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info(
                "法人{}授权平台已取消，经办人未授权，使用法人证书进行企业签署，签署失败:{}",
                legalName,
                JSONObject.toJSONString(fileDigestSignResult_4));
        Assert.assertEquals(fileDigestSignResult_4.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult_4.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 删除法人账号
        deleteLegalAccount();
        // 法人授权经办人-签署场景
        authPsn();
    }

    // 授权经办人
    private void authPsn() {
        FileDigestSignResult fileDigestSignResult;
        String legalRepId; // 法人账号ID

        // 经办人无法人授权，经办人使用法人证书签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info("经办人无法人授权，经办人使用法人证书签署失败:{}", JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 授权经办人
        OnlineCreateLegalRepAuthParam authPsnParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName,
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        true,
                        legalMobile,
                        true,
                        AUTH_TYPE_PSN,
                        personAccountId,
                        "法人授权经办人印章范围信息0V0",
                        "法人授权经办人文件信息wqw",
                        notifyUrl,
                        redirectUrl);
        OnlineCreateAuthResult legalAuthPsnResult =
                serviceClient.authService().createLegalAuth(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 0);
        authId_toPsn = legalAuthPsnResult.getAuthId();
        log.info("首次授权经办人,authId_toPsn：{}", authId_toPsn);

        // 2：根据authId ，查询授权信息
        AuthInfoResult authInfoResult = Operations.checkAuthStatus(serviceClient, 1, authId_toPsn);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2); // 授权类型：法人授权
        Assert.assertEquals(authInfoResult.getAuthType(), Integer.valueOf(AUTH_TYPE_PSN)); // 授权模式：
        Assert.assertNotNull(authInfoResult.getLegalRepId()); // 返回法人账号Id
        legalRepId = authInfoResult.getLegalRepId();

        // 授权中，经办人签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info(
                "法人授权经办人-授权中,authId_toPsn:{}，经办人使用法人证书签署失败:{}",
                authId_toPsn,
                JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));

        urlParam.setAuthId(authId_toPsn);
        AuthFlowSignUrlResult urlResult = serviceClient.authService().getAuthFlowUrl(urlParam);
        log.info("获取法人授权经办人签署链接：{}", JSONObject.toJSONString(urlResult));

        // 断点处：需要完成签署
        // 4  完成授权书签署
        Operations.executionAuthSignFlow(serviceClient, legalAuthPsnResult.getAuthId());

        // 5：查询授权信息-已授权完成
        authInfoResult = Operations.checkAuthStatus(serviceClient, 2, authId_toPsn);
        log.info(
                "授权流程-authId_toPsn:{}，授权书签署完成后，查询授权记录-授权状态：{}",
                authId_toPsn,
                authInfoResult.getStatus().toString());

        // 授权完成后，给法人制证  todo: 线上无法执行
        //        log.info("法人：{}授权经办人，授权完成，授权流程：{}，法人制证", legalRepId, authId_toPsn);
        //        Query.setNewDatabase(false);
        //        Account account = Operations.getAccountInfo(authInfoResult.getLegalRepId());
        //        CertInfo certInfo = Operations.getCertInfo(account.getId());
        //        Assert.assertNotNull(certInfo, "签完法人授权书后未制证");
        //        Assert.assertEquals(certInfo.getCertName(), legalName);
        //        log.info("法人：{}授权经办人，授权完成，授权流程：{}，创建的法人账号证书为：{}", legalRepId, authId_toPsn,
        // JSONObject.toJSONString(certInfo));

        // 法人授权经办人，经办人用法人证书签署成功
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        Operations.checkEvi(project, serviceClient, OUTPUT, legalName + ",授权流程id：" + authId_toPsn);
        clear();
        log.info(
                "法人授权经办人-授权完成,授权流程：{}，经办人使用法人证书签署成功:{}",
                authId_toPsn,
                JSONObject.toJSONString(fileDigestSignResult));

        // 只有经办人授权-平台签-授权失败
        fileDigestSignResult = useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));
        log.info("法人只授权经办人，未授权平台-平台使用法人证书签署失败:{}", JSONObject.toJSONString(fileDigestSignResult));

        // 经办人有法人授权，无法人授权，经办人使用企业证书签署：预期签署失败
        FileDigestSignResult orgCertSign = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN, true);
        Assert.assertEquals(orgCertSign.getErrCode(), 335001, "经办人有法人授权，无法人授权，经办人使用企业证书签署成功了");

        // 取消授权
        serviceClient.authService().cancel(authId_toPsn);
        log.info("授权流程authId_toPsn：{}，取消授权", authId_toPsn);
        // 根据authId ，查询授权信息: 已取消
        authInfoResult = Operations.checkAuthStatus(serviceClient, 4, authId_toPsn);

        // 取消授权-签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));
        log.info(
                "法人授权经办人,authId_toPsn：{}-取消授权，经办人使用法人证书签署失败:{}",
                authId_toPsn,
                JSONObject.toJSONString(fileDigestSignResult));

        // 已有法人账号，重新发起授权
        authPsnParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName + "不",
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        true,
                        legalMobile,
                        true,
                        AUTH_TYPE_PSN,
                        personAccountId,
                        "法人授权经办人印章范围信息0V0",
                        "法人授权经办人文件信息wqw",
                        notifyUrl,
                        redirectUrl);
        legalAuthPsnResult = serviceClient.authService().createLegalAuth(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 339002);
        //        Assert.assertTrue(legalAuthPsnResult.getMsg().contains("账号部分字段冲突：name"));
        log.info(
                "已有法人账号:{}，发起授权法人姓名不一致:{}",
                legalRepId,
                JSONObject.toJSONString(fileDigestSignResult));

        authPsnParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName,
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        true,
                        legalMobile,
                        true,
                        AUTH_TYPE_PSN,
                        personAccountId,
                        "法人授权经办人印章范围信息0V0",
                        "法人授权经办人文件信息wqw",
                        notifyUrl,
                        redirectUrl);
        legalAuthPsnResult = serviceClient.authService().createLegalAuth(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 0);
        authId_toPsn = legalAuthPsnResult.getAuthId();

        log.info(
                "已有法人账号，重新发起授权经办人:authId_toPsn：{}，返回信息：{}",
                authId_toPsn,
                JSONObject.toJSONString(legalAuthPsnResult));

        //         2：根据authId ，查询授权信息
        authInfoResult = Operations.checkAuthStatus(serviceClient, 1, authId_toPsn);
        Assert.assertEquals(legalRepId, authInfoResult.getLegalRepId(), "未使用已有的法人账号");
        log.info("已有法人账号:{},重新发起授权经办人,使用已有的法人账号", legalRepId);

        urlParam.setAuthId(authId_toPsn);
        urlResult = serviceClient.authService().getAuthFlowUrl(urlParam);
        log.info("获取法人授权经办人签署链接：{}", JSONObject.toJSONString(urlResult));

        // 4  完成授权书签署

        Operations.executionAuthSignFlow(serviceClient, authId_toPsn);
        // 已有授权-发起签署成功

        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        //        删除法人账号 断点处：
        deleteLegalAccount();
        //         删除法人账号后，发起签署
        log.info("删除法人账号后{}，经办人使用法人账号发起签署", legalRepId);
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 331003);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号信息异常或不存在"));

        //        serviceClient.authService().cancel(authId_toPsn);

        log.info("再次发起授权- 拒绝签署授权流程"); //
        authPsnParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName,
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        true,
                        legalMobile,
                        true,
                        AUTH_TYPE_PSN,
                        personAccountId,
                        "法人授权经办人印章范围信息0V0",
                        "法人授权经办人文件信息wqw",
                        notifyUrl,
                        redirectUrl);
        legalAuthPsnResult = serviceClient.authService().createLegalAuth(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 0);
        authId_toPsn = legalAuthPsnResult.getAuthId();
        log.info("再次发起授权- 拒绝签署授权流程:authId_toPsn:{}", authId_toPsn);

        log.info("拒绝授权书签署:{}", authId_toPsn);
        // 拒绝签署，断点处：
        // Operations.refuseAuthSignFlow(serviceClient, project, authId_toPsn);
        log.info("查询授权信息- 状态为拒绝:{}", authId_toPsn);
        authInfoResult = Operations.checkAuthStatus(serviceClient, 3, authId_toPsn);
        legalRepId = authInfoResult.getLegalRepId();
        log.info("本次授权，法人账号Id:{},更新legalRepId变量", legalRepId);

        // 法人拒绝签署书，签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));

        log.info("再次发起授权-签署流程过期");
        authPsnParam =
                buildOnlineCreateLegalRepAuthParam(
                        orgAccountId,
                        legalName,
                        IdNoTypeEnum.MAINLAND,
                        legalIdNo,
                        true,
                        legalMobile,
                        true,
                        AUTH_TYPE_PSN,
                        personAccountId,
                        "法人授权经办人印章范围信息0V0",
                        "法人授权经办人文件信息wqw",
                        notifyUrl,
                        redirectUrl);
        legalAuthPsnResult = serviceClient.authService().createLegalAuth(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 0);
        authId_toPsn = legalAuthPsnResult.getAuthId();
        log.info("再次发起法人授权经办人-签署流程过期:{},authId_toPsn:{}", authId_toPsn);
        // 2：根据authId ，查询授权信息
        authInfoResult = Operations.checkAuthStatus(serviceClient, 1, authId_toPsn);
        Assert.assertEquals(legalRepId, authInfoResult.getLegalRepId(), "未使用已有的法人账号");
        log.info("查询新授权记录：{}信息{}", authId_toPsn, JSONObject.toJSONString(authInfoResult));
        log.info("法人授权经办人-授权书过期未签署");
        Operations.expireAuthSignFlow(serviceClient, authId_toPsn);
        // 查询授权信息- 状态为授权失败
        authInfoResult = Operations.checkAuthStatus(serviceClient, 3, authId_toPsn);
        // 清除文件
        clear();
    }

    /**
     * @param signModule 1：平台用法人证书签署 2：经办人用法人证书签署 // * @param signSuccess
     *     是否签署成功，签署成功则发起验签、以及签署证据报告验证 // * @param logMessage 请求日志消息 // * @param authId
     *     证据报告中，验证预期的授权记录 boolean signSuccess, String logMessage, String authId
     */
    private FileDigestSignResult useLegalCertSign(int signModule, boolean... uesOrgCert) {
        //  经办人已授权-经办人可使用法人证书进行签署
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setSignBizType(2); // 签署业务类型:法人证书签署
        orgSignParam.setAccountId(orgAccountId);
        if (uesOrgCert.length > 0 && uesOrgCert[0]) {
            orgSignParam.setSignBizType(1);
        }

        if (PSN_USE_LEGAL_CERT_SIGN == signModule) {
            orgSignParam.setWillingnessAccountId(personAccountId);
            if (null == willingnessId) {
                willingnessId =
                        Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
            }
            orgSignParam.setWillingnessId(willingnessId);
        }

        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        return fileDigestSignResult;
    }

    /** desc:构建创建法人授权的入参对象 */
    public OnlineCreateLegalRepAuthParam buildOnlineCreateLegalRepAuthParam(
            String organizeId,
            String name,
            IdNoTypeEnum idNoType,
            String idNo,
            Boolean encrypt,
            String contact,
            boolean sendNotice,
            int authType,
            String personId,
            String sealScope,
            String fileType,
            String notifyUrl,
            String redirectUrl) {
        OnlineCreateLegalRepAuthParam authParam = new OnlineCreateLegalRepAuthParam();
        authParam.setName(name);
        authParam.setIdNo(idNo);
        authParam.setIdNoType(idNoType);
        authParam.setEncrypt(encrypt);
        authParam.setOrganizeId(organizeId);
        authParam.setPersonId(personId);
        if (authType != 10) {
            authParam.setAuthType(authType);
        }
        authParam.setContact(contact);
        authParam.setSendNotice(sendNotice);
        authParam.setSealScope(sealScope);
        authParam.setFileType(fileType);

        authParam.setNotifyUrl(notifyUrl);
        authParam.setRedirectUrl(redirectUrl);

        return authParam;
    }

    @Test(priority = 99)
    public void clearAccounts() {
        log.info("开始删除个人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
        // 法人账号删除
        deleteLegalAccount();

        log.info("开始删除企业账号：");
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);

        queryAccountInfoParam.setIdNo(orgIdNo_2);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    private void deleteLegalAccount() {
        log.info("开始删除法人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(legalIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    @BeforeMethod
    public void clear() {
        Operations.clearFile();
    }
}
