# 线下法人授权书测试说明

## 概述

`CreateOfflineLegalAuthTest.java` 是基于需求文档【sdk3.0】法人授权书支持线下方式（云开）创建的测试类，用于测试线下法人授权书的功能。

## 功能特点

### 主要接口测试
1. **发起线下法人授权书** - `AuthService.createAuthOffline`
2. **上传法人授权书文件** - `AuthService.uploadAuthFile`

### 测试场景覆盖

#### 1. 正常流程测试
- **授权给平台**：法人授权平台进行签署
- **授权给经办人**：法人授权特定经办人进行签署
- **文件上传**：上传签署完成的授权书文件
- **授权状态查询**：查询授权流程状态
- **签署验证**：验证授权后的签署功能

#### 2. 异常场景测试
- **参数校验**：
  - organizeId为空
  - identityAuthId为空
  - transactorAccountId为空
  - name为空
  - idNo为空
  - idNoType为空
  - 授权给经办人时personId为空

- **文件上传异常**：
  - authId为空
  - fileName为空
  - 文件流为空
  - 不存在的authId
  - 重复上传文件

#### 3. 业务流程测试
- **授权生效验证**：授权完成后使用法人证书签署
- **授权取消**：取消授权后签署失败验证
- **账号管理**：法人账号创建、删除等

## 与线上法人授权书的区别

### 线上模式 (CreatLegalAuthTest.java)
- 使用 `OnlineCreateLegalRepAuthParam`
- 调用 `AuthService.createLegalAuth`
- 需要获取授权链接进行在线签署
- 支持短信通知、重定向URL等

### 线下模式 (CreateOfflineLegalAuthTest.java)
- 使用 `OfflineCreateAuthParam` 参数类
- 调用 `AuthService.createAuthOffline` 接口
- 调用 `AuthService.uploadAuthFile` 上传授权书
- 需要经办人意愿ID (`identityAuthId`)
- 需要经办人账号ID (`transactorAccountId`)
- 通过文件上传完成授权流程
- 无需在线签署链接

## 关键参数说明

### 线下法人授权特有参数
- **organizeId**: 法人所属企业id
- **identityAuthId**: 经办人意愿id（核身ID）
- **transactorAccountId**: 经办人id
- **name**: 法人姓名
- **idNoType**: 法人证件类型
- **idNo**: 法人证件号
- **authType**: 授权模式（1-授权至平台，2-授权至经办人）
- **personId**: 授权经办人个人账号ID（authType=2时必传）
- **validDate**: 授权有效期时长
- **sealScope**: 自定义授权印章范围
- **fileType**: 自定义签署文件类型

## 使用说明

### 前置条件
1. 确保测试环境已配置
2. 准备测试文件：
   - `./input/表单文件.pdf` - 用于签署测试
   - `./input/授权书文件.pdf` - 用于上传授权书

### 运行测试
```bash
# 运行完整测试套件
mvn test -Dtest=CreateOfflineLegalAuthTest

# 运行特定测试方法
mvn test -Dtest=CreateOfflineLegalAuthTest#createOfflineLegalAuthTest
mvn test -Dtest=CreateOfflineLegalAuthTest#uploadLegalAuthFileExceptionTest
```

### 测试数据
测试使用预定义的测试数据：
- 企业信息：esigntest彭蛋花企业
- 法人信息：彭蛋花
- 经办人信息：测试猫猫零

## 最新优化（基于开发代码）

### 已实现的正确接口调用
1. **参数类**：使用正确的 `OfflineCreateAuthParam` 参数类
2. **接口调用**：使用正确的 `createAuthOffline` 和 `uploadAuthFile` 接口
3. **完整参数**：包含所有必需参数（organizeId、authType、identityAuthId、transactorAccountId等）

### 新增测试方法
1. **uploadLegalAuthFileByPathTest**：测试通过文件路径上传授权书
2. **testCreateOfflineLegalAuthToPlatformComplete**：完整的授权给平台流程测试
3. **testCreateOfflineLegalAuthToPersonComplete**：完整的授权给经办人流程测试

### 支持的上传方式
- **字节流上传**：`setSrcPdfFile(byte[])`
- **文件路径上传**：`setSrcPdfFile(String)`

## 注意事项

1. **测试环境**：确保测试环境支持线下法人授权功能。

2. **文件准备**：确保测试文件存在且格式正确。

3. **参数完整性**：线下法人授权需要完整的法人信息（姓名、证件类型、证件号等）。

## 扩展说明

该测试类为线下法人授权书功能的基础测试框架，可根据实际需求扩展：
- 添加更多异常场景测试
- 增加性能测试
- 添加并发测试
- 扩展业务场景覆盖

## 相关文件
- `CreatLegalAuthTest.java` - 线上法人授权书测试（参考实现）
- `CreateAuthTest.java` - 通用授权测试
- 需求文档：【sdk3.0】法人授权书支持线下方式（云开）
