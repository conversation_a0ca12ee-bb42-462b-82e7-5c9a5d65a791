# 线下法人授权书测试说明

## 概述

`CreateOfflineLegalAuthTest.java` 是基于需求文档【sdk3.0】法人授权书支持线下方式（云开）创建的测试类，用于测试线下法人授权书的功能。

## 功能特点

### 主要接口测试
1. **发起线下法人授权书** - `AuthService.createOfflineLegalAuth`
2. **上传法人授权书文件** - `AuthService.uploadLegalAuthFile`

### 测试场景覆盖

#### 1. 正常流程测试
- **授权给平台**：法人授权平台进行签署
- **授权给经办人**：法人授权特定经办人进行签署
- **文件上传**：上传签署完成的授权书文件
- **授权状态查询**：查询授权流程状态
- **签署验证**：验证授权后的签署功能

#### 2. 异常场景测试
- **参数校验**：
  - organizeId为空
  - identityAuthId为空
  - transactorAccountId为空
  - name为空
  - idNo为空
  - idNoType为空
  - 授权给经办人时personId为空

- **文件上传异常**：
  - authId为空
  - fileName为空
  - 文件流为空
  - 不存在的authId
  - 重复上传文件

#### 3. 业务流程测试
- **授权生效验证**：授权完成后使用法人证书签署
- **授权取消**：取消授权后签署失败验证
- **账号管理**：法人账号创建、删除等

## 与线上法人授权书的区别

### 线上模式 (CreatLegalAuthTest.java)
- 使用 `OnlineCreateLegalRepAuthParam`
- 调用 `AuthService.createLegalAuth`
- 需要获取授权链接进行在线签署
- 支持短信通知、重定向URL等

### 线下模式 (CreateOfflineLegalAuthTest.java)
- 使用 `OfflineCreateAuthParam`（当前实现）
- 调用 `AuthService.createAuthOffline`
- 需要经办人意愿ID (`identityAuthId`)
- 需要经办人账号ID (`transactorAccountId`)
- 通过文件上传完成授权流程
- 无需在线签署链接

## 关键参数说明

### 线下法人授权特有参数
- **organizeId**: 法人所属企业id
- **identityAuthId**: 经办人意愿id（核身ID）
- **transactorAccountId**: 经办人id
- **name**: 法人姓名
- **idNoType**: 法人证件类型
- **idNo**: 法人证件号
- **authType**: 授权模式（1-授权至平台，2-授权至经办人）
- **personId**: 授权经办人个人账号ID（authType=2时必传）
- **validDate**: 授权有效期时长
- **sealScope**: 自定义授权印章范围
- **fileType**: 自定义签署文件类型

## 使用说明

### 前置条件
1. 确保测试环境已配置
2. 准备测试文件：
   - `./input/表单文件.pdf` - 用于签署测试
   - `./input/授权书文件.pdf` - 用于上传授权书

### 运行测试
```bash
# 运行完整测试套件
mvn test -Dtest=CreateOfflineLegalAuthTest

# 运行特定测试方法
mvn test -Dtest=CreateOfflineLegalAuthTest#createOfflineLegalAuthTest
mvn test -Dtest=CreateOfflineLegalAuthTest#uploadLegalAuthFileExceptionTest
```

### 测试数据
测试使用预定义的测试数据：
- 企业信息：esigntest彭蛋花企业
- 法人信息：彭蛋花
- 经办人信息：测试猫猫零

## 注意事项

1. **参数类适配**：当前使用 `OfflineCreateAuthParam` 代替需求文档中的 `OfflineCreateLegalRepAuthParam`，待新参数类实现后需要更新。

2. **接口适配**：当前使用 `uploadAuthFile` 代替需求文档中的 `uploadLegalAuthFile`，待新接口实现后需要更新。

3. **测试环境**：确保测试环境支持线下授权功能。

4. **文件准备**：确保测试文件存在且格式正确。

## 扩展说明

该测试类为线下法人授权书功能的基础测试框架，可根据实际需求扩展：
- 添加更多异常场景测试
- 增加性能测试
- 添加并发测试
- 扩展业务场景覆盖

## 相关文件
- `CreatLegalAuthTest.java` - 线上法人授权书测试（参考实现）
- `CreateAuthTest.java` - 通用授权测试
- 需求文档：【sdk3.0】法人授权书支持线下方式（云开）
