package test.auth;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.IdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.OrganRegTypeEnum;
import com.util.Client;
import com.util.data.param.Params;
import com.util.file.FileUtil;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.*;

import java.io.IOException;
import java.util.Arrays;

// 法人静默签授权（授权至平台/经办人，线下模式）
@Slf4j
public class CreateOfflineLegalAuthTest {
    final boolean enabled = true;

    private static final int AUTH_TYPE_PLATFORM = 1; // 授权给平台
    private static final int AUTH_TYPE_PSN = 2; // 授权给经办人
    private static final int PSN_USE_LEGAL_CERT_SIGN = 1; // 平台使用法人证书签署
    private static final int PLATFORM_USE_LEGAL_CERT_SIGN = 2; // 经办人使用法人证书签署

    private String orgAccountId;
    private String orgAccountId_2;
    private ServiceClient serviceClient;
    private String personAccountId;
    private String identityAuthId; // 经办人意愿id

    private Project project;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String AUTH_FILE_PATH = "./input/授权书文件.pdf";
    private static final String OUTPUT = "./output/11111.pdf";
    private byte[] fileStream;
    private byte[] authFileStream;
    private String willingnessId;
    private String authId_toPsn;
    private String authId_toPlatform;
    private String orgSealData;

    private AddAccountResult accountInfoResult_org;
    private AddAccountResult accountInfoResult_org_2;
    private AddAccountResult accountInfoResult_psn;
    private String notifyUrl = Operations.getCALL_BACK();

    private String psnMobile = "***********"; // 企业经办人账号
    private String psnName = "测试猫猫零"; // 企业经办人账号
    private String psnIdNo = "432427189712163920"; // 企业经办人账号

    // 法人账号信息
    private String legalMobile = "***********";
    private String legalIdNo = "432427189510305353";
    private String legalName = "彭蛋花";
    private String orgIdNo = "9100000066135592GF";
    private String orgName = "esigntest彭蛋花企业";

    private String orgName_2 = "彭蛋花企业";
    private String orgIdNo_2 = "9100000004975632UX";

    @BeforeClass
    @Parameters({"env"})
    private void init(String env) throws IOException {
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getProject();
        serviceClient = Client.getClient(env, project);
        // 预先清除数据
        clearAccounts();
        Operations.mock(Arrays.asList(legalMobile, psnMobile));
        fileStream = FileUtil.getFileStream(FILE_PATH);
        authFileStream = FileUtil.getFileStream(AUTH_FILE_PATH);

        // 创建一个企业账号，法人授权使用
        OrganizeParam organizeParam = new OrganizeParam();
        organizeParam.setOrgCode(orgIdNo);
        organizeParam.setRegType(OrganRegTypeEnum.MERGE);
        organizeParam.setName(orgName);
        accountInfoResult_org = serviceClient.accountService().addAccount(organizeParam);
        orgAccountId = accountInfoResult_org.getAccountId();
        orgSealData = Params.getOrgSealData(serviceClient, orgName);

        // 创建企业账号2，测试使用
        organizeParam.setName(orgName_2);
        organizeParam.setOrgCode(orgIdNo_2);
        accountInfoResult_org_2 = serviceClient.accountService().addAccount(organizeParam);
        orgAccountId_2 = accountInfoResult_org_2.getAccountId();

        // 创建经办人账号
        PersonParam personParam = new PersonParam();
        personParam.setIdNo(psnIdNo);
        personParam.setName(psnName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        accountInfoResult_psn = serviceClient.accountService().addAccount(personParam);
        personAccountId = accountInfoResult_psn.getAccountId();

        // 获取经办人意愿id
        identityAuthId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
    }

    @DataProvider
    public Object[][] createOfflineAuthDataException() {
        int authType = 1;
        boolean encrypt = false;
        String sealScope = "合同专用章、人事专用章";
        String fileType = "贷款协议、运输协议、物流条款";
        int validDate = 1; // 1年

        return new Object[][] {
            {
                "organizeId为空", // caseName
                "", // organizeId
                identityAuthId, // identityAuthId
                personAccountId, // transactorAccountId
                legalName, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "organizeId不能为空"
            },
            {
                "identityAuthId为空", // caseName
                orgAccountId, // organizeId
                "", // identityAuthId
                personAccountId, // transactorAccountId
                legalName, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "identityAuthId不能为空"
            },
            {
                "transactorAccountId为空", // caseName
                orgAccountId, // organizeId
                identityAuthId, // identityAuthId
                "", // transactorAccountId
                legalName, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "transactorAccountId不能为空"
            },
            {
                "name为空", // caseName
                orgAccountId, // organizeId
                identityAuthId, // identityAuthId
                personAccountId, // transactorAccountId
                "", // name
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "name不能为空"
            },
            {
                "idNo为空", // caseName
                orgAccountId, // organizeId
                identityAuthId, // identityAuthId
                personAccountId, // transactorAccountId
                legalName, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                "", // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "idNo不能为空"
            },
            {
                "idNoType为空", // caseName
                orgAccountId, // organizeId
                identityAuthId, // identityAuthId
                personAccountId, // transactorAccountId
                legalName, // name
                null, // idNoType
                legalIdNo, // idNo
                encrypt,
                authType,
                null, // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "idNoType不能为空"
            },
            {
                "授权给经办人账号-不传经办人oid", // caseName
                orgAccountId, // organizeId
                identityAuthId, // identityAuthId
                personAccountId, // transactorAccountId
                legalName, // name
                IdNoTypeEnum.MAINLAND, // idNoType
                legalIdNo, // idNo
                encrypt,
                2, // authType
                "", // personId
                validDate,
                sealScope,
                fileType,
                notifyUrl,
                10001,
                "授权至经办人时，个人账号不能为空"
            }
        };
    }

    @Test(dataProvider = "createOfflineAuthDataException", enabled = true)
    public void createOfflineLegalRepExceptTest(
            String caseName,
            String organizeId,
            String identityAuthId,
            String transactorAccountId,
            String name,
            IdNoTypeEnum idNoType,
            String idNo,
            Boolean encrypt,
            int authType,
            String personId,
            int validDate,
            String sealScope,
            String fileType,
            String notifyUrl,
            int errCode,
            String msg) {

        OfflineCreateAuthParam authParam = buildOfflineCreateLegalRepAuthParam(
                organizeId, identityAuthId, transactorAccountId, name, idNoType, idNo,
                encrypt, authType, personId, validDate, sealScope, fileType, notifyUrl);

        OfflineCreateAuthResult result = serviceClient.authService().createAuthOffline(authParam);
        log.info("{}场景返回：{}", caseName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), errCode);
        if (result.getErrCode() != 0) {
            Assert.assertTrue(result.getMsg().contains(msg));
        }
    }

    /** 线下法人授权平台_测试场景 */
    @Test(enabled = enabled)
    public void createOfflineLegalAuthTest() {
        String legalRepId;
        String scope = "天命人，原力觉醒。清风拂过山岗，静夜思，举头望明月，低头思故乡。月是故乡明！！！";

        // 1：发起线下授权：授权给平台
        OfflineCreateAuthParam authParam = new OfflineCreateAuthParam();
        authParam.setOrganizeId(orgAccountId);
        authParam.setAuthType(AUTH_TYPE_PLATFORM);
        authParam.setValidDate(1);
        authParam.setSealScope(scope);
        authParam.setFileType(scope);
        authParam.setNotifyUrl(notifyUrl);
        authParam.setTransactorAccountId(personAccountId);
        authParam.setIdentityAuthId(identityAuthId);

        log.info("发起线下法人授权平台签署：{}", JSONObject.toJSONString(authParam));
        OfflineCreateAuthResult legalAuthResult = serviceClient.authService().createAuthOffline(authParam);
        Assert.assertEquals(legalAuthResult.getErrCode(), 0);
        authId_toPlatform = legalAuthResult.getAuthId();
        String fileUrl = legalAuthResult.getFileUrl();
        log.info("发起线下法人授权平台签署,authId_toPlatform:{}, fileUrl:{}", authId_toPlatform, fileUrl);
        Assert.assertNotNull(fileUrl, "授权书下载地址不能为空");

        // 2：根据authId，查询授权信息
        AuthInfoResult authInfoResult = Operations.checkAuthStatus(serviceClient, 1, authId_toPlatform);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2);
        Assert.assertNotNull(authInfoResult.getLegalRepId());
        legalRepId = authInfoResult.getLegalRepId();
        log.info("查询授权记录信息：{}", JSONObject.toJSONString(authInfoResult));

        // 3：上传法人授权书文件
        UploadAuthFileParam uploadParam = new UploadAuthFileParam();
        uploadParam.setAuthId(authId_toPlatform);
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setFileName("法人授权书.pdf");

        Result uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("上传法人授权书文件：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);
        Assert.assertEquals(uploadResult.getMsg(), "成功");

        // 4：查询授权信息-已授权完成
        authInfoResult = Operations.checkAuthStatus(serviceClient, 2, authId_toPlatform);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2);
        log.info("授权书上传完成后，查询授权记录-授权状态：{}", authInfoResult.getStatus().toString());

        // 授权完成后，给法人制证
        Account account = Operations.getAccountInfo(authInfoResult.getLegalRepId());
        log.info("查询法人账号：【{}】信息,返回{}", authInfoResult.getLegalRepId(), JSONObject.toJSONString(account));
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        log.info("查询法人账号：【{}】证书信息,返回{}", account.getId(), JSONObject.toJSONString(certInfo));

        Assert.assertNotNull(certInfo, "上传法人授权书后未制证");
        Assert.assertEquals(certInfo.getCertName(), legalName);
        log.info("创建的法人账号证书为：{}", JSONObject.toJSONString(certInfo));

        // 法人已授权平台，使用法人证书-可使用平台代法人签署
        FileDigestSignResult fileDigestSignResult = useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        log.info("法人{}授权平台签署，平台可使用法人证书签署:{}", legalName, JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        Operations.checkEvi(project, serviceClient, OUTPUT, legalName + ",授权流程id：" + authId_toPlatform);
        clear();

        // 取消授权
        serviceClient.authService().cancel(authId_toPlatform);
        authInfoResult = Operations.checkAuthStatus(serviceClient, 4, authId_toPlatform);
        log.info("查询授权记录：{}", JSONObject.toJSONString(authInfoResult));

        // 法人授权平台已取消，平台使用法人证书签署，签署失败
        FileDigestSignResult fileDigestSignResult_3 = useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        log.info("法人{}授权平台签署已取消，平台使用法人证书签署失败:{}", legalName, JSONObject.toJSONString(fileDigestSignResult_3));
        Assert.assertEquals(fileDigestSignResult_3.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult_3.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 删除法人账号
        deleteLegalAccount();
        // 法人授权经办人-签署场景
        authPsn();
    }

    // 授权经办人
    private void authPsn() {
        FileDigestSignResult fileDigestSignResult;
        String legalRepId; // 法人账号ID

        // 经办人无法人授权，经办人使用法人证书签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info("经办人无法人授权，经办人使用法人证书签署失败:{}", JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 授权经办人
        OfflineCreateAuthParam authPsnParam = new OfflineCreateAuthParam();
        authPsnParam.setOrganizeId(orgAccountId);
        authPsnParam.setAuthType(AUTH_TYPE_PSN);
        authPsnParam.setPersonId(personAccountId);
        authPsnParam.setValidDate(1);
        authPsnParam.setSealScope("法人授权经办人印章范围信息0V0");
        authPsnParam.setFileType("法人授权经办人文件信息wqw");
        authPsnParam.setNotifyUrl(notifyUrl);
        authPsnParam.setTransactorAccountId(personAccountId);
        authPsnParam.setIdentityAuthId(identityAuthId);

        OfflineCreateAuthResult legalAuthPsnResult = serviceClient.authService().createAuthOffline(authPsnParam);
        Assert.assertEquals(legalAuthPsnResult.getErrCode(), 0);
        authId_toPsn = legalAuthPsnResult.getAuthId();
        log.info("首次授权经办人,authId_toPsn：{}", authId_toPsn);

        // 2：根据authId，查询授权信息
        AuthInfoResult authInfoResult = Operations.checkAuthStatus(serviceClient, 1, authId_toPsn);
        Assert.assertEquals(authInfoResult.getAuthorizerType(), 2); // 授权类型：法人授权
        Assert.assertEquals(authInfoResult.getAuthType(), Integer.valueOf(AUTH_TYPE_PSN)); // 授权模式：
        Assert.assertNotNull(authInfoResult.getLegalRepId()); // 返回法人账号Id
        legalRepId = authInfoResult.getLegalRepId();

        // 授权中，经办人签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        log.info("法人授权经办人-授权中,authId_toPsn:{}，经办人使用法人证书签署失败:{}", authId_toPsn, JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));

        // 3：上传法人授权书文件
        UploadAuthFileParam uploadParam = new UploadAuthFileParam();
        uploadParam.setAuthId(authId_toPsn);
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setFileName("法人授权经办人授权书.pdf");

        Result uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("上传法人授权经办人授权书文件：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);

        // 5：查询授权信息-已授权完成
        authInfoResult = Operations.checkAuthStatus(serviceClient, 2, authId_toPsn);
        log.info("授权流程-authId_toPsn:{}，授权书上传完成后，查询授权记录-授权状态：{}", authId_toPsn, authInfoResult.getStatus().toString());

        // 法人授权经办人，经办人用法人证书签署成功
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        Operations.checkEvi(project, serviceClient, OUTPUT, legalName + ",授权流程id：" + authId_toPsn);
        clear();
        log.info("法人授权经办人-授权完成,授权流程：{}，经办人使用法人证书签署成功:{}", authId_toPsn, JSONObject.toJSONString(fileDigestSignResult));

        // 只有经办人授权-平台签-授权失败
        fileDigestSignResult = useLegalCertSign(PLATFORM_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));
        log.info("法人只授权经办人，未授权平台-平台使用法人证书签署失败:{}", JSONObject.toJSONString(fileDigestSignResult));

        // 取消授权
        serviceClient.authService().cancel(authId_toPsn);
        log.info("授权流程authId_toPsn：{}，取消授权", authId_toPsn);
        // 根据authId，查询授权信息: 已取消
        authInfoResult = Operations.checkAuthStatus(serviceClient, 4, authId_toPsn);

        // 取消授权-签署失败
        fileDigestSignResult = useLegalCertSign(PSN_USE_LEGAL_CERT_SIGN);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("签署失败: 账号授权记录不存在"));
        log.info("法人授权经办人,authId_toPsn：{}-取消授权，经办人使用法人证书签署失败:{}", authId_toPsn, JSONObject.toJSONString(fileDigestSignResult));

        // 删除法人账号
        deleteLegalAccount();
        // 清除文件
        clear();
    }

    /**
     * @param signModule 1：平台用法人证书签署 2：经办人用法人证书签署
     */
    private FileDigestSignResult useLegalCertSign(int signModule, boolean... uesOrgCert) {
        // 经办人已授权-经办人可使用法人证书进行签署
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setSignBizType(2); // 签署业务类型:法人证书签署
        orgSignParam.setAccountId(orgAccountId);
        if (uesOrgCert.length > 0 && uesOrgCert[0]) {
            orgSignParam.setSignBizType(1);
        }

        if (PSN_USE_LEGAL_CERT_SIGN == signModule) {
            orgSignParam.setWillingnessAccountId(personAccountId);
            if (null == willingnessId) {
                willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
            }
            orgSignParam.setWillingnessId(willingnessId);
        }

        FileDigestSignResult fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        return fileDigestSignResult;
    }

    /** desc:构建创建线下法人授权的入参对象 */
    public OfflineCreateAuthParam buildOfflineCreateLegalRepAuthParam(
            String organizeId,
            String identityAuthId,
            String transactorAccountId,
            String name,
            IdNoTypeEnum idNoType,
            String idNo,
            Boolean encrypt,
            int authType,
            String personId,
            int validDate,
            String sealScope,
            String fileType,
            String notifyUrl) {
        OfflineCreateAuthParam authParam = new OfflineCreateAuthParam();
        authParam.setOrganizeId(organizeId);
        authParam.setIdentityAuthId(identityAuthId);
        authParam.setTransactorAccountId(transactorAccountId);
        if (authType != 10) {
            authParam.setAuthType(authType);
        }
        authParam.setPersonId(personId);
        authParam.setValidDate(validDate);
        authParam.setSealScope(sealScope);
        authParam.setFileType(fileType);
        authParam.setNotifyUrl(notifyUrl);

        return authParam;
    }

    @Test(priority = 99)
    public void clearAccounts() {
        log.info("开始删除个人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
        // 法人账号删除
        deleteLegalAccount();

        log.info("开始删除企业账号：");
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);

        queryAccountInfoParam.setIdNo(orgIdNo_2);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    private void deleteLegalAccount() {
        log.info("开始删除法人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(legalIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    @BeforeMethod
    public void clear() {
        Operations.clearFile();
    }

    /** 测试上传法人授权书文件的异常场景 */
    @Test(enabled = enabled)
    public void uploadLegalAuthFileExceptionTest() {
        // 先创建一个授权
        OfflineCreateAuthParam authParam = new OfflineCreateAuthParam();
        authParam.setOrganizeId(orgAccountId);
        authParam.setAuthType(AUTH_TYPE_PLATFORM);
        authParam.setValidDate(1);
        authParam.setSealScope("测试印章范围");
        authParam.setFileType("测试文件类型");
        authParam.setNotifyUrl(notifyUrl);
        authParam.setTransactorAccountId(personAccountId);
        authParam.setIdentityAuthId(identityAuthId);

        OfflineCreateAuthResult legalAuthResult = serviceClient.authService().createAuthOffline(authParam);
        Assert.assertEquals(legalAuthResult.getErrCode(), 0);
        String testAuthId = legalAuthResult.getAuthId();

        // 测试authId为空
        UploadAuthFileParam uploadParam = new UploadAuthFileParam();
        uploadParam.setAuthId("");
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setFileName("测试文件.pdf");

        Result uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("authId为空场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 10001);
        Assert.assertTrue(uploadResult.getMsg().contains("authId不能为空"));

        // 测试fileName为空
        uploadParam.setAuthId(testAuthId);
        uploadParam.setFileName("");
        uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("fileName为空场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 10001);
        Assert.assertTrue(uploadResult.getMsg().contains("fileName不能为空"));

        // 测试文件流为空
        uploadParam.setFileName("测试文件.pdf");
        uploadParam.setSrcPdfFile(null);
        uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("文件流为空场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 10001);
        Assert.assertTrue(uploadResult.getMsg().contains("文件不能为空"));

        // 测试不存在的authId
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setAuthId("999999999");
        uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("不存在的authId场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 332001);
        Assert.assertTrue(uploadResult.getMsg().contains("授权记录不存在"));

        // 正常上传
        uploadParam.setAuthId(testAuthId);
        uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("正常上传场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);

        // 重复上传
        uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("重复上传场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 332011);
        Assert.assertTrue(uploadResult.getMsg().contains("授权流程状态进行中不支持再次上传授权文件"));
    }

    /** 测试通过文件路径上传授权书 */
    @Test(enabled = enabled)
    public void uploadLegalAuthFileByPathTest() {
        // 先创建一个授权
        OfflineCreateAuthParam authParam = new OfflineCreateAuthParam();
        authParam.setOrganizeId(orgAccountId);
        authParam.setAuthType(AUTH_TYPE_PLATFORM);
        authParam.setValidDate(1);
        authParam.setSealScope("测试印章范围");
        authParam.setFileType("测试文件类型");
        authParam.setNotifyUrl(notifyUrl);
        authParam.setTransactorAccountId(personAccountId);
        authParam.setIdentityAuthId(identityAuthId);

        OfflineCreateAuthResult legalAuthResult = serviceClient.authService().createAuthOffline(authParam);
        Assert.assertEquals(legalAuthResult.getErrCode(), 0);
        String testAuthId = legalAuthResult.getAuthId();

        // 通过文件路径上传
        UploadAuthFileParam uploadParam = new UploadAuthFileParam();
        uploadParam.setAuthId(testAuthId);
        uploadParam.setStreamFile(AUTH_FILE_PATH);
        uploadParam.setFileName("授权书文件.pdf");

        Result uploadResult = serviceClient.authService().uploadAuthFile(uploadParam);
        log.info("通过文件路径上传场景返回：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);
        Assert.assertEquals(uploadResult.getMsg(), "成功");
    }

    /** 测试线下法人授权给平台的完整流程 */
    @Test(enabled = enabled)
    public void testCreateOfflineLegalAuthToPlatformComplete() {
        // 1. 发起线下法人授权给平台
        OfflineCreateLegalRepAuthParam authParam = new OfflineCreateLegalRepAuthParam();
        authParam.setOrganizeId(orgAccountId);
        authParam.setName(legalName);
        authParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        authParam.setIdNo(legalIdNo);
        authParam.setEncrypt(false);
        authParam.setAuthType(AUTH_TYPE_PLATFORM);
        authParam.setValidDate(1);
        authParam.setSealScope("合同专用章");
        authParam.setFileType("借贷协议");
        authParam.setNotifyUrl(notifyUrl);
        authParam.setTransactorAccountId(personAccountId);
        authParam.setIdentityAuthId(identityAuthId);

        log.info("【发起线下法人授权参数】：{}", JSONObject.toJSONString(authParam));
        OfflineCreateAuthResult result = serviceClient.authService().createOfflineLegalAuth(authParam);
        log.info("【发起线下法人授权结果】：{}", JSONObject.toJSONString(result));
        Assert.assertTrue(result.getErrCode() == 0);
        Assert.assertNotNull(result.getAuthId());
        Assert.assertNotNull(result.getFileUrl());

        // 2. 查询授权状态
        AuthInfoResult authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
        Assert.assertEquals(authInfo.getStatus(), Integer.valueOf(1)); // 授权中

        // 3. 上传授权书
        UploadLegalAuthFileParam uploadParam = new UploadLegalAuthFileParam();
        uploadParam.setAuthId(result.getAuthId());
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setFileName("法人授权书.pdf");

        Result uploadResult = serviceClient.authService().uploadLegalAuthFile(uploadParam);
        log.info("【上传授权书结果】：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);

        // 4. 再次查询授权状态
        authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【上传后查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
        Assert.assertEquals(authInfo.getStatus(), Integer.valueOf(2)); // 已授权

        // 5. 取消授权
        serviceClient.authService().cancel(result.getAuthId());
        authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【取消授权后查询结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getStatus(), Integer.valueOf(4)); // 已取消
    }

    /** 测试线下法人授权给经办人的完整流程 */
    @Test(enabled = enabled)
    public void testCreateOfflineLegalAuthToPersonComplete() {
        // 1. 发起线下法人授权给经办人
        OfflineCreateLegalRepAuthParam authParam = new OfflineCreateLegalRepAuthParam();
        authParam.setOrganizeId(orgAccountId);
        authParam.setName(legalName);
        authParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        authParam.setIdNo(legalIdNo);
        authParam.setEncrypt(false);
        authParam.setAuthType(AUTH_TYPE_PSN);
        authParam.setPersonId(personAccountId);
        authParam.setValidDate(1);
        authParam.setSealScope("合同专用章");
        authParam.setFileType("借贷协议");
        authParam.setNotifyUrl(notifyUrl);
        authParam.setTransactorAccountId(personAccountId);
        authParam.setIdentityAuthId(identityAuthId);

        log.info("【发起线下法人授权经办人参数】：{}", JSONObject.toJSONString(authParam));
        OfflineCreateAuthResult result = serviceClient.authService().createOfflineLegalAuth(authParam);
        log.info("【发起线下法人授权经办人结果】：{}", JSONObject.toJSONString(result));
        Assert.assertTrue(result.getErrCode() == 0);
        Assert.assertNotNull(result.getAuthId());
        Assert.assertNotNull(result.getFileUrl());

        // 2. 查询授权状态
        AuthInfoResult authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
        Assert.assertEquals(authInfo.getAuthType(), Integer.valueOf(AUTH_TYPE_PSN));
        Assert.assertEquals(authInfo.getPersonId(), personAccountId);

        // 3. 上传授权书
        UploadLegalAuthFileParam uploadParam = new UploadLegalAuthFileParam();
        uploadParam.setAuthId(result.getAuthId());
        uploadParam.setSrcPdfFile(authFileStream);
        uploadParam.setFileName("法人授权经办人授权书.pdf");

        Result uploadResult = serviceClient.authService().uploadLegalAuthFile(uploadParam);
        log.info("【上传授权书结果】：{}", JSONObject.toJSONString(uploadResult));
        Assert.assertEquals(uploadResult.getErrCode(), 0);

        // 4. 再次查询授权状态
        authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【上传后查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
        Assert.assertEquals(authInfo.getStatus(), Integer.valueOf(2)); // 已授权
    }

    /** 测试线下法人授权的简化方法（参考开发代码风格） */
    @Test(enabled = enabled)
    public void testCreateOfflineLegalAuthSimple() {
        // 发起线下法人授权给平台
        OfflineCreateAuthResult result = createOfflineLegalAuthToPlatform(null, null, null);
        Assert.assertTrue(result.getErrCode() == 0);

        // 上传授权书
        testUploadLegalAuthFile(result.getAuthId());

        // 查询授权结果
        AuthInfoResult authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
    }

    /** 测试线下法人授权给经办人的简化方法 */
    @Test(enabled = enabled)
    public void testCreateOfflineLegalAuthToPersonSimple() {
        // 发起线下法人授权给经办人
        OfflineCreateAuthResult result = createOfflineLegalAuthToPerson(null, null, null);
        Assert.assertTrue(result.getErrCode() == 0);

        // 上传授权书
        testUploadLegalAuthFile(result.getAuthId());

        // 查询授权结果
        AuthInfoResult authInfo = serviceClient.authService().queryAuth(result.getAuthId());
        log.info("【查询授权结果】：{}", JSONObject.toJSONString(authInfo));
        Assert.assertEquals(authInfo.getErrCode(), 0);
        Assert.assertEquals(authInfo.getAuthType(), Integer.valueOf(AUTH_TYPE_PSN));
    }

    /** 创建线下法人授权给平台（参考开发代码风格） */
    private OfflineCreateAuthResult createOfflineLegalAuthToPlatform(String organizeId, String name, String idNo) {
        OfflineCreateLegalRepAuthParam param = new OfflineCreateLegalRepAuthParam();
        param.setOrganizeId(organizeId != null ? organizeId : orgAccountId);
        param.setName(name != null ? name : legalName);
        param.setIdNoType(IdNoTypeEnum.MAINLAND);
        param.setIdNo(idNo != null ? idNo : legalIdNo);
        param.setEncrypt(false);
        param.setAuthType(AUTH_TYPE_PLATFORM);
        param.setValidDate(1);
        param.setFileType("借贷协议");
        param.setSealScope("合同专用章");
        param.setNotifyUrl(notifyUrl);
        param.setTransactorAccountId(personAccountId);
        param.setIdentityAuthId(identityAuthId);

        log.info("【发起线下法人授权参数】：{}", JSONObject.toJSONString(param));
        OfflineCreateAuthResult result = serviceClient.authService().createOfflineLegalAuth(param);
        log.info("【发起线下法人授权结果】：{}", JSONObject.toJSONString(result));
        return result;
    }

    /** 创建线下法人授权给经办人（参考开发代码风格） */
    private OfflineCreateAuthResult createOfflineLegalAuthToPerson(String organizeId, String name, String idNo) {
        OfflineCreateLegalRepAuthParam param = new OfflineCreateLegalRepAuthParam();
        param.setOrganizeId(organizeId != null ? organizeId : orgAccountId);
        param.setName(name != null ? name : legalName);
        param.setIdNoType(IdNoTypeEnum.MAINLAND);
        param.setIdNo(idNo != null ? idNo : legalIdNo);
        param.setEncrypt(false);
        param.setAuthType(AUTH_TYPE_PSN);
        param.setPersonId(personAccountId);
        param.setValidDate(1);
        param.setFileType("借贷协议");
        param.setSealScope("合同专用章");
        param.setNotifyUrl(notifyUrl);
        param.setTransactorAccountId(personAccountId);
        param.setIdentityAuthId(identityAuthId);

        log.info("【发起线下法人授权经办人参数】：{}", JSONObject.toJSONString(param));
        OfflineCreateAuthResult result = serviceClient.authService().createOfflineLegalAuth(param);
        log.info("【发起线下法人授权经办人结果】：{}", JSONObject.toJSONString(result));
        return result;
    }

    /** 上传法人授权书文件（参考开发代码风格） */
    private void testUploadLegalAuthFile(String authId) {
        UploadLegalAuthFileParam param = new UploadLegalAuthFileParam();
        param.setAuthId(authId);
        param.setFileName("法人授权书.pdf");
        param.setSrcPdfFile(authFileStream);

        Result result = serviceClient.authService().uploadLegalAuthFile(param);
        log.info("【上传授权书结果】：{}", JSONObject.toJSONString(result));
        Assert.assertTrue(result.getErrCode() == 0);
    }
}
