package test.migration;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.OrganizeParam;
import com.timevale.esign.paas.tech.bean.request.PersonParam;
import com.timevale.esign.paas.tech.bean.request.PersonSignParam;
import com.timevale.esign.paas.tech.bean.request.QueryAccountInfoParam;
import com.timevale.esign.paas.tech.bean.request.UpdateOrganizeParam;
import com.timevale.esign.paas.tech.bean.request.UpdatePersonParam;
import com.timevale.esign.paas.tech.bean.result.AccountInfoResult;
import com.timevale.esign.paas.tech.bean.result.AddAccountResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.Result;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.PersonTemplateType;
import com.timevale.esign.paas.tech.enums.SealColor;
import com.timevale.esign.paas.tech.enums.StampRuleEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;
import com.util.data.param.Params;
import com.util.operation.Operations;
import com.util.report.ExtentTestNGIReporterListener;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class AccountMigrationGrayStage2Test {
    private Project period3Project;
    private ServiceClient period3ServiceClient;
    private Project period2Project;
    private ServiceClient period2ServiceClient;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private String phone = "***********";

    @BeforeClass
    @Parameters({"env", "title", "branch", "buildId", "secret", "token", "projectName"})
    public void init(
            String env,
            @Optional String title,
            @Optional String branch,
            @Optional String buildId,
            @Optional String secret,
            @Optional String token,
            @Optional String projectName) {
        ExtentTestNGIReporterListener.setEnv(env);
        Client.setTimevaleData(env);
        period3Project = Client.getTimevaleData().getProject();
        period3ServiceClient = Client.getClient(env, period3Project);
        period2Project = new Project("**********", "aa87a9d6800294693339dc9d975a5361");
        period2ServiceClient = Client.getClient(env, period2Project);
    }

    @Test(enabled = true)
    public void period3PersonTest() {
        log.info("正在执行阶段2，不在灰度时创建个人账号场景，当前appId为：{}", period3Project.getId());
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        AddAccountResult addAccountResult =
                period3ServiceClient.accountService().addAccount(personParam);
        log.info("创建{}，身份证为{}，返回：{}", name, idNo, JSONObject.toJSONString(addAccountResult));
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), name);
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertTrue(
                account.getId() < Integer.MAX_VALUE,
                String.format("非灰度appId，账号存在老库，id应当小于22亿，实际为：%s", account.getId()));
        log.info("查询老库有数据");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNotNull(account, "数据应该落到老库");
        Assert.assertEquals(account.getName(), name);
        Assert.assertTrue(
                account.getId() < Integer.MAX_VALUE,
                String.format("老库的id应当小于22亿，实际为：%s", account.getId()));
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(addAccountResult.getAccountId());
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        Result result = period3ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName);
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertEquals(account.getName(), newName);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
    }

    @Test(enabled = true)
    public void period3OrganizationTest() {
        log.info("正在执行阶段2，不在灰度时创建企业账号场景，当前appId为：{}", period3Project.getId());
        String orgCode = IdCard.getSocialCode("91");
        String name = "esign" + Names.getChineseName() + "经营的个体工商户";
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, name);
        AddAccountResult addAccountResult =
                period3ServiceClient.accountService().addAccount(organizeParam);
        log.info("创建{}，证件号{}，返回：{}", name, orgCode, JSONObject.toJSONString(addAccountResult));
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), name);
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertTrue(
                account.getId() < Integer.MAX_VALUE,
                String.format("非灰度appId，账号存到老库，id应当小于22亿，实际为：%s", account.getId()));
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertEquals(account.getName(), name);
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(addAccountResult.getAccountId());
        String newName = "esign" + Names.getChineseName() + "经营的个体工商户";
        updateOrganizeParam.setName(newName);
        Result result = period3ServiceClient.accountService().updateAccount(updateOrganizeParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName);
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertEquals(account.getName(), newName);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
    }

    @Test
    public void period2PersonTest() {
        log.info("正在执行阶段2，灰度时创建个人账号场景，当前appId为：{}", period2Project.getId());
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        AddAccountResult addAccountResult =
                period2ServiceClient.accountService().addAccount(personParam);
        log.info("创建{}，身份证为{}，返回：{}", name, idNo, JSONObject.toJSONString(addAccountResult));
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertTrue(
                account.getId() > Integer.MAX_VALUE,
                String.format("灰度appId，账号存到新库，id应当大于22亿，实际为：%s", account.getId()));
        Assert.assertEquals(account.getName(), name);
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(addAccountResult.getAccountId());
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        Result result = period2ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        log.info("查询新库数据被更新");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertEquals(account.getName(), newName);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId = Operations.getWillingnessId(period2Project, idNo, phone, name, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setAccountId(addAccountResult.getAccountId());
        personSignParam.setSealData(Client.getTimevaleData().getSealData());
        FileDigestSignResult fileDigestSignResult =
                period2ServiceClient.userSignService().personSign(personSignParam);
        Operations.verifyPdf(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
    }

    @Test
    public void period2OrganizationTest() {
        log.info("正在执行阶段2，灰度时创建企业账号场景，当前appId为：{}", period2Project.getId());
        String orgCode = IdCard.getSocialCode("91");
        String name = "esign" + Names.getChineseName() + "经营的个体工商户";
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, name);
        AddAccountResult addAccountResult =
                period2ServiceClient.accountService().addAccount(organizeParam);
        log.info("创建{}，证件号{}，返回：{}", name, orgCode, JSONObject.toJSONString(addAccountResult));
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertTrue(
                account.getId() > Integer.MAX_VALUE,
                String.format("灰度appId，账号存到新库，id应当大于22亿，实际为：%s", account.getId()));
        Assert.assertEquals(account.getName(), name);
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(addAccountResult.getAccountId());
        String newName = "esign" + Names.getChineseName() + "经营的个体工商户";
        updateOrganizeParam.setName(newName);
        Result result = period2ServiceClient.accountService().updateAccount(updateOrganizeParam);
        Assert.assertEquals(result.getErrCode(), 0);
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertEquals(account.getName(), newName, "数据未更新");
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
    }

    @Test
    public void newDataUpdate() {
        log.info("查询新库的数据");
        String idNo = "510182197202157789";
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period2ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        String personId = accountInfoResult.getAccountId();
        Account account = Operations.getAccountInfo(personId);
        Assert.assertTrue(account.getId() > Integer.MAX_VALUE, "id应当大于22亿");
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setAccountId(personId);
        Result result = period2ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period2ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName, "账号更新失败");
        log.info("直接调接口可以更新成功。");
        //        Query.setNewDatabase(false);
        account = Operations.getAccountInfo(personId);
        Assert.assertTrue(
                account.getId() > Integer.MAX_VALUE,
                String.format("新库id应当大于22亿，实际为：%s", account.getId()));
        //        Assert.assertNull(account, "老库不应该有数据");
        //        Query.setNewDatabase(true);
        //        account = Operations.getAccountInfo(personId);
        //        Assert.assertNotNull(account, "新库应该有数据");
        Assert.assertEquals(account.getName(), newName, "新库数据未更新");
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId =
                Operations.getWillingnessId(period2Project, idNo, phone, newName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(
                period2ServiceClient
                        .templateSealService()
                        .createPsnSeal(
                                accountInfoResult.getAccountId(),
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE)
                        .getSealData());
        personSignParam.setAccountId(accountInfoResult.getAccountId());
        FileDigestSignResult fileDigestSignResult =
                period2ServiceClient.userSignService().personSign(personSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
        Operations.checkEvi(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName + ",个人运营商三要素认证,电子签名信息未被篡改,认证信息");
    }

    @Test
    public void oldDataUpdate() {
        log.info("查询老库的数据");
        String idNo = "522326200105233856";
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        log.info("直接调接口可以查询到数据。");
        String personId = accountInfoResult.getAccountId();
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        String newName = Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setAccountId(personId);
        Result result = period3ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName, "账号更新失败");
        log.info("直接调接口可以更新成功。");
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(personId);
        Assert.assertEquals(account.getName(), newName, "老库数据被更新");
        Assert.assertTrue(
                account.getId() < Integer.MAX_VALUE,
                String.format("老库id应当小于22亿，实际为：%s", account.getId()));
        //        Query.setNewDatabase(true);
        //        account = Operations.getAccountInfo(personId);
        //        Assert.assertNull(account, "新库不应该有数据");
    }
}
