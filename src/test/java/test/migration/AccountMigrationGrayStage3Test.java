package test.migration;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.PersonParam;
import com.timevale.esign.paas.tech.bean.request.PersonSignParam;
import com.timevale.esign.paas.tech.bean.request.QueryAccountInfoParam;
import com.timevale.esign.paas.tech.bean.request.UpdatePersonParam;
import com.timevale.esign.paas.tech.bean.result.AccountInfoResult;
import com.timevale.esign.paas.tech.bean.result.AddAccountResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.Result;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.PersonTemplateType;
import com.timevale.esign.paas.tech.enums.SealColor;
import com.timevale.esign.paas.tech.enums.StampRuleEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;
import com.util.data.param.Params;
import com.util.operation.Operations;
import com.util.report.ExtentTestNGIReporterListener;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class AccountMigrationGrayStage3Test {
    private Project period3Project;
    private ServiceClient period3ServiceClient;
    private Project period2Project;
    private ServiceClient period2ServiceClient;
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private String phone = "***********";

    @BeforeClass
    @Parameters({"env", "title", "branch", "buildId", "secret", "token", "projectName"})
    public void init(
            String env,
            @Optional String title,
            @Optional String branch,
            @Optional String buildId,
            @Optional String secret,
            @Optional String token,
            @Optional String projectName) {
        ExtentTestNGIReporterListener.setEnv(env);
        Client.setTimevaleData(env);
        period3Project = Client.getTimevaleData().getProject();
        period3ServiceClient = Client.getClient(env, period3Project);
        period2Project = new Project("**********", "aa87a9d6800294693339dc9d975a5361");
        period2ServiceClient = Client.getClient(env, period2Project);
    }

    @Test(enabled = true)
    public void period3Test() {
        log.info("正在执行{}的场景", period3Project.getId());
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        AddAccountResult addAccountResult =
                period3ServiceClient.accountService().addAccount(personParam);
        log.info("创建账号，返回：{}", JSONObject.toJSONString(addAccountResult));
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), name);
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNull(account, "数据不应该落到老库");
        log.info("查询新库可以查到");
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNotNull(account, "数据应该落到新库");
        Assert.assertEquals(account.getName(), name);
        Assert.assertEquals(account.getAuthProject(), period3Project.getId());
        Assert.assertTrue(
                account.getId() > 220_000_000L,
                String.format("新库的id应当大于22亿，实际为：%s", account.getId()));
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(addAccountResult.getAccountId());
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        Result result = period3ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        //        Query.setNewDatabase(false);
        log.info("查询老库没有数据");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNull(account, "数据不应该落到老库");
        //        Query.setNewDatabase(true);
        log.info("查询新库有数据");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNotNull(account, "数据应该落到新库");
        Assert.assertEquals(account.getName(), newName);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId =
                Operations.getWillingnessId(period3Project, idNo, phone, newName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(
                period3ServiceClient
                        .templateSealService()
                        .createPsnSeal(
                                accountInfoResult.getAccountId(),
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE)
                        .getSealData());
        personSignParam.setAccountId(accountInfoResult.getAccountId());
        FileDigestSignResult fileDigestSignResult =
                period3ServiceClient.userSignService().personSign(personSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(
                period3Project,
                period3ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
        Operations.checkEvi(
                period3Project,
                period3ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName + ",个人运营商三要素认证,电子签名信息未被篡改,认证信息");
    }

    @Test
    public void period2Test() {
        log.info("正在执行{}的场景", period2Project.getId());
        String idNo = IdCard.getIdNo();
        String name = "测试" + Names.getChineseName(idNo);
        PersonParam personParam = Params.getPersonParam(idNo, name);
        AddAccountResult addAccountResult =
                period2ServiceClient.accountService().addAccount(personParam);
        log.info("创建账号，返回：{}", JSONObject.toJSONString(addAccountResult));
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period2ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), name);
        log.info("查询老库没有数据");
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNull(account, "数据不应该落到老库");
        log.info("查询新库可以查到");
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNotNull(account, "数据应该落到新库");
        Assert.assertEquals(account.getName(), name);
        Assert.assertEquals(account.getAuthProject(), period2Project.getId());
        Assert.assertTrue(
                account.getId() > 220_000_000L,
                String.format("新库的id应当大于22亿，实际为：%s", account.getId()));
        Assert.assertEquals(account.getAccountUid(), addAccountResult.getAccountId());
        CertInfo certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), name);
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(addAccountResult.getAccountId());
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        Result result = period2ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        //        Query.setNewDatabase(false);
        log.info("查询老库没有数据");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNull(account, "数据不应该落到老库");
        //        Query.setNewDatabase(true);
        log.info("查询新库数据被更新");
        account = Operations.getAccountInfo(addAccountResult.getAccountId());
        Assert.assertNotNull(account, "数据应该落到新库");
        Assert.assertEquals(account.getName(), newName);
        certInfo = Operations.getCertInfo(account.getId());
        Assert.assertNotNull(certInfo, "未创建证书");
        Assert.assertEquals(certInfo.getCertName(), newName);
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId =
                Operations.getWillingnessId(period2Project, idNo, phone, newName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(
                period2ServiceClient
                        .templateSealService()
                        .createPsnSeal(
                                accountInfoResult.getAccountId(),
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE)
                        .getSealData());
        personSignParam.setAccountId(accountInfoResult.getAccountId());
        FileDigestSignResult fileDigestSignResult =
                period2ServiceClient.userSignService().personSign(personSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
        Operations.checkEvi(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName + ",个人运营商三要素认证,电子签名信息未被篡改,认证信息");
    }

    @Test(enabled = true)
    public void newDataUpdate() {
        log.info("查询插入新库的数据");
        String idNo = "350623197505199000";
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period2ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        String personId = accountInfoResult.getAccountId();
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setAccountId(personId);
        Result result = period2ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period2ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName, "账号更新失败");
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(personId);
        Assert.assertNull(account);
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(personId);
        Assert.assertNotNull(account, "新库应该有数据");
        Assert.assertEquals(account.getName(), newName, "新库数据被更新");
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId =
                Operations.getWillingnessId(period2Project, idNo, phone, newName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(
                period2ServiceClient
                        .templateSealService()
                        .createPsnSeal(
                                accountInfoResult.getAccountId(),
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE)
                        .getSealData());
        personSignParam.setAccountId(accountInfoResult.getAccountId());
        FileDigestSignResult fileDigestSignResult =
                period2ServiceClient.userSignService().personSign(personSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
        Operations.checkEvi(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName + ",个人运营商三要素认证,电子签名信息未被篡改,认证信息");
    }

    @Test
    public void oldDataUpdate() {
        log.info("查询插入老库的数据");
        String idNo = "330127198603011760";
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        String personId = accountInfoResult.getAccountId();
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        String newName = "测试" + Names.getChineseName(idNo);
        updatePersonParam.setName(newName);
        updatePersonParam.setAccountId(personId);
        Result result = period3ServiceClient.accountService().updateAccount(updatePersonParam);
        Assert.assertEquals(result.getErrCode(), 0);
        accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getName(), newName, "账号更新失败");
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(personId);
        Assert.assertNull(account);
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(personId);
        Assert.assertNotNull(account, "新库应该有数据");
        Assert.assertEquals(account.getName(), newName, "新库数据被更新");
        PersonSignParam personSignParam = Params.getPersonSignParam(FILE_PATH);
        String willingnessId =
                Operations.getWillingnessId(period3Project, idNo, phone, newName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(
                period3ServiceClient
                        .templateSealService()
                        .createPsnSeal(
                                accountInfoResult.getAccountId(),
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE)
                        .getSealData());
        personSignParam.setAccountId(accountInfoResult.getAccountId());
        FileDigestSignResult fileDigestSignResult =
                period3ServiceClient.userSignService().personSign(personSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Operations.verifyPdf(
                period3Project,
                period3ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName);
        Operations.checkEvi(
                period2Project,
                period2ServiceClient,
                fileDigestSignResult.getDstFilePath(),
                newName + ",个人运营商三要素认证,电子签名信息未被篡改,认证信息");
    }

    @Test
    public void queryTest() {
        log.info("阶段3的projectId：{}在阶段3查询仅存在新库的数据", period3Project.getId());
        String idNo = "431026200312171732";
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getErrCode(), 0);
        String personId = accountInfoResult.getAccountId();
        //        Query.setNewDatabase(false);
        Account account = Operations.getAccountInfo(personId);
        Assert.assertNull(account);
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(personId);
        Assert.assertNotNull(account);
        Assert.assertEquals(account.getName(), accountInfoResult.getName());

        log.info("阶段3的projectId：{}在阶段3查询仅存在老库的数据", period3Project.getId());
        idNo = "440515200002071938";
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        accountInfoResult =
                period3ServiceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getErrCode(), 0);
        personId = accountInfoResult.getAccountId();
        //        Query.setNewDatabase(true);
        account = Operations.getAccountInfo(personId);
        Assert.assertNull(account);
        //        Query.setNewDatabase(false);
        account = Operations.getAccountInfo(personId);
        Assert.assertNotNull(account);
        Assert.assertEquals(account.getName(), accountInfoResult.getName());
    }
}
