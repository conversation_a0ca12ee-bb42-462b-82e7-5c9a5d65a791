package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.AccountTypeEnum;
import com.timevale.esign.paas.tech.enums.AllIdNoTypeEnum;
import com.util.Client;
import com.util.data.IdCard;
import com.util.data.Names;
import com.util.data.param.Params;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class DigitalSignTest { // 文本签
    private final boolean enabled = true;

    private Project project;
    private ServiceClient serviceClient, serviceClientWithSM2;
    private String name, idNo, psnId, psnId2;
    private String orgName, orgCode, orgId;

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        log.info("当前环境为：{}", env);
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getWeakAuth();
        serviceClient = Client.getClient(env, project);
        serviceClientWithSM2 = Client.getClient(env, Client.getTimevaleData().getProject());
        idNo = IdCard.getIdNo();
        name = Names.getChineseName(idNo);
        orgCode = IdCard.getSocialCode("91");
        orgName = "esigntest" + name + "经营的个体工商户";
    }

    @Test(priority = 0)
    public void createPsnAccount() {
        log.info("开始创建个人账户：");
        PersonParam personParam = Params.getPersonParam(idNo, name);
        AddAccountResult addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnId = addAccountResult.getAccountId();
        log.info("创建个人用户{}，返回：{}", name, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        psnId2 = serviceClientWithSM2.accountService().addAccount(personParam).getAccountId();

        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, orgName);
        addAccountResult = serviceClient.accountService().addAccount(organizeParam);
        orgId = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", orgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
    }

    @Test(priority = 1, enabled = enabled)
    public void localDigitalSignWithP1Test() {
        DigitalSignBean digitalSignBean = new DigitalSignBean();
        String plainText =
                "01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789";
        log.info("关键字长度为：{}", plainText.length());
        digitalSignBean.setPlainText(plainText);
        DigitalSignWithP1Result digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("未传accountId不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP1Result.getMsg(), "缺少参数: accountId");

        digitalSignBean.setAccountId("");
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("accountId为空不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP1Result.getMsg(), "缺少参数: accountId");

        digitalSignBean.setAccountId("1");
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("accountId不存在不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 331003);
        Assert.assertTrue(digitalSignWithP1Result.getMsg().contains("账号信息异常或不存在"));

        digitalSignBean.setPlainText(null);
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("未传plainText不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP1Result.getMsg(), "缺少参数: plainText");

        digitalSignBean.setPlainText("");
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("plainText为空不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP1Result.getMsg(), "缺少参数: plainText");

        digitalSignBean.setPlainText(plainText + 1);
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("plainText为5001个字符不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 1000056);
        Assert.assertTrue(digitalSignWithP1Result.getMsg().contains("plainText超过数据长度最大值"));

        digitalSignBean.setAccountId(psnId2);
        digitalSignBean.setPlainText(plainText);
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("accountId非当前应用不能签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 331004);
        Assert.assertTrue(digitalSignWithP1Result.getMsg().contains("非当前应用appId"));

        digitalSignBean.setAccountId(psnId);
        digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("plainText为5000个字符可以签署：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 0);
        Assert.assertTrue(digitalSignWithP1Result.getMsg().contains("成功"));

        VerifyDigitalSignP1Bean verifyDigitalSignBean = new VerifyDigitalSignP1Bean();
        verifyDigitalSignBean.setSignature(digitalSignWithP1Result.getSignature());
        verifyDigitalSignBean.setAlgorithm(digitalSignWithP1Result.getAlgorithm());
        verifyDigitalSignBean.setCert(digitalSignWithP1Result.getCertBean().getCert());
        verifyDigitalSignBean.setTimestampSignature(
                digitalSignWithP1Result.getTimestampSignature());
        VerifyDigestSignResult verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("未传plainText，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("plainText"));

        verifyDigitalSignBean.setPlainText("");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("plainText为空字符串，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("plainText"));

        verifyDigitalSignBean.setPlainText(plainText + 1);
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("plainText超过数据长度最大值，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000056);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("plainText超过数据长度最大值"));

        verifyDigitalSignBean.setPlainText("keyword");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("plainText不匹配，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("签名结果验签失败"));

        verifyDigitalSignBean.setPlainText(plainText);
        verifyDigitalSignBean.setAlgorithm("");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("algorithm为空字符串，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("algorithm"));

        verifyDigitalSignBean.setAlgorithm("111");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("algorithm不匹配，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("签名证书与111算法不匹配"));

        verifyDigitalSignBean.setAlgorithm(digitalSignWithP1Result.getAlgorithm());
        verifyDigitalSignBean.setSignature(null);
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("signature为空不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("signature"));

        verifyDigitalSignBean.setSignature("invalid_signature");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("无效signature不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("signature格式错误"));

        verifyDigitalSignBean.setSignature(digitalSignWithP1Result.getSignature());
        verifyDigitalSignBean.setCert(null);
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("cert为空不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("cert"));

        verifyDigitalSignBean.setCert("null");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("cert格式错误：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("验证失败"));

        verifyDigitalSignBean.setCert(digitalSignWithP1Result.getCertBean().getCert());
        verifyDigitalSignBean.setTimestampSignature("");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("timestampSignature为空字符串可以验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getCert(),
                digitalSignWithP1Result.getCertBean().getCert());
        Assert.assertEquals(verifyDigestSignResult.getCertBean().getCn(), name);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getOu(),
                digitalSignWithP1Result.getCertBean().getOu());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getSn(),
                digitalSignWithP1Result.getCertBean().getSn());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getIssuerCN(),
                digitalSignWithP1Result.getCertBean().getIssuerCN());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getEndDate(),
                digitalSignWithP1Result.getCertBean().getEndDate());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getStartDate(),
                digitalSignWithP1Result.getCertBean().getStartDate());
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("成功"));

        verifyDigitalSignBean.setSignature(digitalSignWithP1Result.getSignature());
        verifyDigitalSignBean.setTimestampSignature(null);
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("timestampSignature为空不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("timestampSignature格式错误"));

        verifyDigitalSignBean.setTimestampSignature("null");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("timestampSignature格式错误：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("验证失败"));

        verifyDigitalSignBean.setTimestampSignature(
                digitalSignWithP1Result.getTimestampSignature());
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("正常输入，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getCert(),
                digitalSignWithP1Result.getCertBean().getCert());
        Assert.assertEquals(verifyDigestSignResult.getCertBean().getCn(), name);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getOu(),
                digitalSignWithP1Result.getCertBean().getOu());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getSn(),
                digitalSignWithP1Result.getCertBean().getSn());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getIssuerCN(),
                digitalSignWithP1Result.getCertBean().getIssuerCN());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getEndDate(),
                digitalSignWithP1Result.getCertBean().getEndDate());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getStartDate(),
                digitalSignWithP1Result.getCertBean().getStartDate());
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("成功"));
    }

    @Test(priority = 1, enabled = enabled)
    public void localDigitalSignWithP7Test() {
        DigitalSignBean digitalSignBean = new DigitalSignBean();
        String plainText =
                "01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789";
        log.info("关键字长度为：{}", plainText.length());
        digitalSignBean.setPlainText(plainText);
        DigitalSignWithP7Result digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("未传accountId不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP7Result.getMsg(), "缺少参数: accountId");

        digitalSignBean.setAccountId("");
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("accountId为空不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP7Result.getMsg(), "缺少参数: accountId");

        digitalSignBean.setAccountId("1");
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("accountId不存在不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 331003);
        Assert.assertTrue(digitalSignWithP7Result.getMsg().contains("账号信息异常或不存在"));

        digitalSignBean.setPlainText(null);
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("未传plainText不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP7Result.getMsg(), "缺少参数: plainText");

        digitalSignBean.setPlainText("");
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("plainText为空不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 1000022);
        Assert.assertEquals(digitalSignWithP7Result.getMsg(), "缺少参数: plainText");

        digitalSignBean.setPlainText(plainText + 1);
        digitalSignBean.setAccountId(psnId);
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("plainText为5001个字符不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 1000056);
        Assert.assertTrue(digitalSignWithP7Result.getMsg().contains("plainText超过数据长度最大值"));

        digitalSignBean.setPlainText(plainText);
        digitalSignBean.setAccountId(psnId2);
        digitalSignWithP7Result =
                serviceClientWithSM2.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("P7不支持SM2算法：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 870015);
        Assert.assertTrue(
                digitalSignWithP7Result
                        .getMsg()
                        .contains(
                                "java.security.NoSuchAlgorithmException: no such algorithm: SM3 for provider SM"));

        digitalSignBean.setAccountId(psnId2);
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("accountId非当前应用不能签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 331004);
        Assert.assertTrue(digitalSignWithP7Result.getMsg().contains("非当前应用appId"));

        digitalSignBean.setAccountId(psnId);
        digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("plainText为5000个字符可以签署：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 0);
        Assert.assertTrue(digitalSignWithP7Result.getMsg().contains("成功"));

        VerifyDigitalSignP7Bean verifyDigitalSignP7Bean = new VerifyDigitalSignP7Bean();
        verifyDigitalSignP7Bean.setPlainText(plainText + 1);
        verifyDigitalSignP7Bean.setSignature(digitalSignWithP7Result.getSignature());
        VerifyDigestSignResult verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("plainText超过数据长度最大值，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000056);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("plainText超过数据长度最大值"));

        verifyDigitalSignP7Bean.setPlainText("plainText");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("plainText不匹配，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("签名结果验签失败"));

        verifyDigitalSignP7Bean.setSignature(null);
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("signature为空不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 1000022);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("signature"));

        verifyDigitalSignP7Bean.setSignature("invalid_signature");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("无效signature不能验签：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 200105);
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("signature格式错误"));

        verifyDigitalSignP7Bean.setPlainText(plainText);
        verifyDigitalSignP7Bean.setSignature(digitalSignWithP7Result.getSignature());
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("正常输入，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getCert(),
                digitalSignWithP7Result.getCertBean().getCert());
        Assert.assertEquals(verifyDigestSignResult.getCertBean().getCn(), name);
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getOu(),
                digitalSignWithP7Result.getCertBean().getOu());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getSn(),
                digitalSignWithP7Result.getCertBean().getSn());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getIssuerCN(),
                digitalSignWithP7Result.getCertBean().getIssuerCN());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getEndDate(),
                digitalSignWithP7Result.getCertBean().getEndDate());
        Assert.assertEquals(
                verifyDigestSignResult.getCertBean().getStartDate(),
                digitalSignWithP7Result.getCertBean().getStartDate());
        Assert.assertTrue(verifyDigestSignResult.getMsg().contains("成功"));
    }

    @Test(priority = 1, enabled = enabled)
    public void orgDigitalSign() {
        DigitalSignBean digitalSignBean = new DigitalSignBean();
        String plainText = "plainText";
        digitalSignBean.setPlainText(plainText);
        digitalSignBean.setAccountId(orgId);
        DigitalSignWithP1Result digitalSignWithP1Result =
                serviceClient.userSignService().localDigitalSignWithP1(digitalSignBean);
        log.info("企业文本签，返回：{}", JSONObject.toJSONString(digitalSignWithP1Result));
        Assert.assertEquals(digitalSignWithP1Result.getErrCode(), 0);
        Assert.assertEquals(digitalSignWithP1Result.getMsg(), "成功");
        VerifyDigitalSignP1Bean verifyDigitalSignBean = new VerifyDigitalSignP1Bean();
        verifyDigitalSignBean.setSignature(digitalSignWithP1Result.getSignature());
        verifyDigitalSignBean.setAlgorithm(digitalSignWithP1Result.getAlgorithm());
        verifyDigitalSignBean.setCert(digitalSignWithP1Result.getCertBean().getCert());
        verifyDigitalSignBean.setTimestampSignature(
                digitalSignWithP1Result.getTimestampSignature());
        verifyDigitalSignBean.setPlainText(plainText);
        VerifyDigestSignResult verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP1(verifyDigitalSignBean);
        log.info("企业P1文本签验签，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(verifyDigestSignResult.getCertBean().getCn(), orgName);
        Assert.assertEquals(verifyDigestSignResult.getMsg(), "成功");

        digitalSignBean.setPlainText("123456");
        DigitalSignWithP7Result digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("企业文本签，返回：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 0);
        Assert.assertEquals(digitalSignWithP7Result.getMsg(), "成功");
        VerifyDigitalSignP7Bean verifyDigitalSignP7Bean = new VerifyDigitalSignP7Bean();
        verifyDigitalSignP7Bean.setSignature(digitalSignWithP7Result.getSignature());
        verifyDigitalSignP7Bean.setPlainText("123456");
        verifyDigestSignResult =
                serviceClient.userSignService().digitalVerifyWithP7(verifyDigitalSignP7Bean);
        log.info("企业P7文本签验签，返回：{}", JSONObject.toJSONString(verifyDigestSignResult));
        Assert.assertEquals(verifyDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(verifyDigestSignResult.getCertBean().getCn(), orgName);
        Assert.assertEquals(verifyDigestSignResult.getMsg(), "成功");
    }

    @Test(priority = 99, enabled = enabled)
    public void clearAccounts() {
        log.info("开始删除个人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(idNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
        Operations.deleteAccount(serviceClientWithSM2, queryAccountInfoParam);
    }

    @Test(
            priority = 100,
            dependsOnMethods = {"clearAccounts"})
    public void signAfterClear() {
        DigitalSignBean digitalSignBean = new DigitalSignBean();
        String plainText = "plainText";
        digitalSignBean.setPlainText(plainText);
        digitalSignBean.setAccountId(psnId);
        DigitalSignWithP7Result digitalSignWithP7Result =
                serviceClient.userSignService().localDigitalSignWithP7(digitalSignBean);
        log.info("账号被删除无法进行文本签：{}", JSONObject.toJSONString(digitalSignWithP7Result));
        Assert.assertEquals(digitalSignWithP7Result.getErrCode(), 331003);
        Assert.assertTrue(digitalSignWithP7Result.getMsg().contains("账号信息异常或不存在"));
    }
}
