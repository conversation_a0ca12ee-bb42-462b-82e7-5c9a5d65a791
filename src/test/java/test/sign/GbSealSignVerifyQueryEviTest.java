package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.*;
import com.util.Client;
import com.util.data.param.Params;
import com.util.file.FileUtil;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import test.sign.dataProvider.GbSealSignVerifyQueryEviDataProvider;

import java.io.File;
import java.io.IOException;

@Slf4j
public class GbSealSignVerifyQueryEviTest { // 企业授权签+经办人授权签+验签+出证
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private byte[] fileStream;
    private static final String OUTPUT = "output/";
    private String psnId;
    private final String psnIdNo = "340822199712270158";
    private final String psnName = "测试宣刚义";
    private final String psnMobile = "16111111111";
    private String psnSealData;
    private String orgId;
    private final String orgCode = "9100000087664478AN";
    private final String orgName = "esigntest李保辉能力有限公司";
    private String orgSealData;
    private ServiceClient serviceClient;
    private String projectOrgName;
    private String willingnessId;
    private String psnSealData_gb;
    private String orgSealData_gb;
    private String sealIdInProjectId;
    private String platSealData;
    private Project project;

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getProject();
        serviceClient = Client.getClient(env, project);
        projectOrgName =
                Client.getTimevaleData()
                        .getProjectOrgName(Client.getTimevaleData().getProject().getId());
    }

    @Test(priority = 0)
    // 初始化，创建账号
    public void createAccount() throws IOException {
        // 获取平台方信息
        String projectId = Client.getTimevaleData().getProject().getId();
        sealIdInProjectId = Client.getTimevaleData().getOrgSealIdByProjectId(projectId);
        platSealData = Client.getTimevaleData().getSealData();
        projectOrgName = Client.getTimevaleData().getProjectOrgName(projectId);
        // 创建账号
        PersonParam personParam = new PersonParam();
        personParam.setIdNo(psnIdNo);
        personParam.setName(psnName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        AddAccountResult addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnId = addAccountResult.getAccountId();
        log.info("创建个人用户{}，返回：{}", psnName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        psnSealData = Params.getPsnSealData(serviceClient, psnName);
        log.info("创建{}的个人base64图片印章", psnName);
        psnSealData_gb = getSealData_gb(AccountTypeEnum.PERSON, psnId, null);
        log.info("创建{}的个人新国标电子印章", psnName);
        OrganizeParam organizeParam = new OrganizeParam();
        organizeParam.setOrgCode(orgCode);
        organizeParam.setRegType(OrganRegTypeEnum.MERGE);
        organizeParam.setName(orgName);
        addAccountResult = serviceClient.accountService().addAccount(organizeParam);
        orgId = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", orgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        projectOrgName = Client.getTimevaleData().getProjectOrgName(projectId);
        fileStream = FileUtil.getFileStream(FILE_PATH);
        // 创建企业印章
        orgSealData = Params.getOrgSealData(serviceClient, orgName);
        log.info("创建{}的企业印章", orgName);
        orgSealData_gb = getSealData_gb(AccountTypeEnum.ORGAN, orgId, null);
        log.info("创建{}的企业新国标电子印章", orgName);
        // 进行企业授权经办人签署
        Operations.onlineAuth(serviceClient, 2, psnMobile, orgId, psnId);
        // 平台授权签署
        Operations.onlineAuth(serviceClient, 1, psnMobile, orgId, psnId);
    }

    @AfterClass
    public void clear() {
        File file = new File(OUTPUT);
        for (File f : file.listFiles()) {
            log.info("删除文件{}", f.getName());
            f.delete();
        }
    }

    /** 平台签 */
    @Test(
            priority = 4,
            enabled = true,
            description = "平台签署测试",
            dataProviderClass = GbSealSignVerifyQueryEviDataProvider.class,
            dataProvider = "platSign_dataProvider")
    public void platformSignTest(
            String caseName,
            String filePathOrStream,
            SealSpecEnum specEnum,
            SealSpecEnum sealDataTyp,
            int errCode) {
        PlatformSignParam platSignParam;
        // 指定文件路径签署/文件流签署
        if ("file".equals(filePathOrStream)) {
            platSignParam = Params.getPlatformSignParam(FILE_PATH);
        } else {
            platSignParam = Params.getPlatformSignParam(fileStream);
        }
        platSignParam.getFileBean().setDstPdfFile(OUTPUT + caseName + "_plat_1.pdf");

        // 指定印章格式 （null 表示不指定参数）
        if (null != specEnum) {
            platSignParam.setSealSpec(specEnum);
        }
        // 指定印章数据 : GB_SEAL:指定sealId , IMAGE：base64图片
        // (不指定印章类型，此时取与签署对象不匹配的新国标印章)
        // 新国标印章本期不暂不支持出证
        if (null == sealDataTyp) {
            platSignParam.setSealData(orgSealData_gb);
        } else {
            switch (sealDataTyp) {
                case GB_SEAL:
                    platSignParam.setSealId(sealIdInProjectId);
                    platSignParam.setSealData(null);
                    break;
                case IMAGE:
                    platSignParam.setSealData(platSealData);
                    break;
            }
        }
        FileDigestSignResult fileDigestSignResult =
                serviceClient.platformSignService().platformSign(platSignParam);
        log.info(
                "平台签【{}】： 签署场景：{}，签署结果：{}，入参：",
                projectOrgName,
                caseName,
                fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), errCode);
    }

    /** 企业签署 */
    @Test(
            priority = 2,
            enabled = true,
            description = "企业用户签",
            dataProviderClass = GbSealSignVerifyQueryEviDataProvider.class,
            dataProvider = "orgSign_dataProvider")
    public void orgSignTest(
            String caseName,
            String filePathOrStream,
            SealSpecEnum specEnum,
            SealSpecEnum sealDataTyp,
            int errCode) {
        OrgSignParam orgSignParam;
        // 指定文件路径签署/文件流签署
        if ("file".equals(filePathOrStream)) {
            orgSignParam = Params.getOrgSignParam(FILE_PATH);
        } else {
            orgSignParam = Params.getOrgSignParam(fileStream);
        }
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.getFileBean().setDstPdfFile(OUTPUT + caseName + "_org_1.pdf");
        // 指定印章格式 （null 表示不指定参数）
        if (null != specEnum) {
            orgSignParam.setSealSpec(specEnum);
        }
        // 指定印章数据 (不指定印章类型，此时取与签署对象不匹配的新国标印章)
        // 新国标印章本期不暂不支持出证
        if (null == sealDataTyp) {
            orgSignParam.setSealData(psnSealData_gb);
        } else {
            switch (sealDataTyp) {
                case GB_SEAL:
                    orgSignParam.setSealData(orgSealData_gb);
                    break;
                case IMAGE:
                    orgSignParam.setSealData(orgSealData);
                    break;
            }
        }
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info(
                "企业：{}授权经办人：{} 签署场景：{}，签署结果：{}",
                orgName,
                psnName,
                caseName,
                fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), errCode);
    }

    @Test(
            priority = 1,
            enabled = true,
            description = "个人用户签署测试",
            dataProviderClass = GbSealSignVerifyQueryEviDataProvider.class,
            dataProvider = "orgSign_dataProvider")
    public void personSignTest(
            String caseName,
            String filePathOrStream,
            SealSpecEnum specEnum,
            SealSpecEnum sealDataTyp,
            int errCode) {
        PersonSignParam personSignParam;
        // 指定文件路径签署/文件流签署
        if ("file".equals(filePathOrStream)) {
            personSignParam = Params.getPersonSignParam(FILE_PATH);
        } else {
            personSignParam = Params.getPersonSignParam(fileStream);
        }
        personSignParam.getFileBean().setDstPdfFile(OUTPUT + caseName + "_psn_1.pdf");
        // 指定印章格式 （null 表示不指定参数）
        if (null != specEnum) {
            personSignParam.setSealSpec(specEnum);
        }
        // 指定印章数据 : GB_SEAL:指定sealId , IMAGE：base64图片
        // (不指定印章类型，此时取与签署对象不匹配的新国标印章)
        // 新国标印章本期不暂不支持出证
        if (null == sealDataTyp) {
            personSignParam.setSealData(orgSealData_gb);
        } else {
            switch (sealDataTyp) {
                case GB_SEAL:
                    personSignParam.setSealData(psnSealData_gb);
                    break;
                case IMAGE:
                    personSignParam.setSealData(psnSealData);
                    break;
            }
        }
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        personSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().personSign(personSignParam);
        log.info(
                "平台签【{}】： 签署场景：{}，签署结果：{}，入参：",
                projectOrgName,
                caseName,
                fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), errCode);
    }

    /**
     * 创建新国标印章
     *
     * @param accountTypeEnum
     * @param accountId
     * @param sealImage
     * @return
     */
    private String getSealData_gb(
            AccountTypeEnum accountTypeEnum, String accountId, String sealImage) {
        AddGbSealResult addSealResult;
        if (AccountTypeEnum.PERSON == accountTypeEnum) {
            GbPersonalSealParam gbPersonalSealParam = new GbPersonalSealParam();
            gbPersonalSealParam.setAccountId(accountId);
            gbPersonalSealParam.setSealImage(sealImage);
            gbPersonalSealParam.setType(PersonTemplateType.SQUARE);
            addSealResult =
                    serviceClient.templateSealService().createGbPersonalSeal(gbPersonalSealParam);

        } else {
            GbOfficialSealParam gbOfficialSealParam = new GbOfficialSealParam();
            gbOfficialSealParam.setAccountId(accountId);
            gbOfficialSealParam.setSealImage(sealImage);
            gbOfficialSealParam.setType(OrganizeTemplateType.STAR);
            addSealResult =
                    serviceClient.templateSealService().createGbOfficialSeal(gbOfficialSealParam);
        }
        log.info("创建新国标印章：{}", JSONObject.toJSONString(addSealResult));
        Assert.assertEquals(addSealResult.getErrCode(), 0);
        Assert.assertTrue(addSealResult.getMsg().contains("成功"));
        Assert.assertNotNull(addSealResult.getSealData());

        // 创建印章成功， 可正确解析印章
        ParseSealResult parseSealResult =
                serviceClient.templateSealService().sealInfoParse(addSealResult.getSealData());
        log.info("解析新国标印章，返回：{}", parseSealResult.getMsg());

        return addSealResult.getSealData();
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error("", e);
        }
    }

    private void sleep() {
        sleep(5000);
    }

    @Test(priority = 100)
    public void clearAccount() {
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        psnId = serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        // 删除个人账号
        Result result = serviceClient.accountService().deleteAccount(psnId);
        log.info("删除accountId为{}的个人用户{}，返回：{}", psnId, psnName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        //        Assert.assertTrue(result.getMsg().contains("成功"));
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        orgId = serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        // 删除企业账号
        result = serviceClient.accountService().deleteAccount(orgId);
        log.info("删除企业用户{}，accountId为{}", orgName, orgId);
        Assert.assertEquals(result.getErrCode(), 0);
        //        Assert.assertTrue(result.getMsg().contains("成功"));
        // 查询个人账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam);
        log.info("删除个人账号接口返回：{}", JSONObject.toJSONString(accountInfoResult));
        Assert.assertEquals(accountInfoResult.getErrCode(), 331009);
        log.info("证件号为{}的个人用户已被删除", psnIdNo);
        // 查询企业账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        accountInfoResult = serviceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getErrCode(), 331009);
        log.info("删除企业账号接口返回：{}", JSONObject.toJSONString(accountInfoResult));
        log.info("证件号为{}的企业用户已被删除", orgCode);
    }
}
