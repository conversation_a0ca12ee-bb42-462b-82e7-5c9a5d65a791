package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.timevale.esign.paas.tech.bean.bean.PosBean;
import com.timevale.esign.paas.tech.bean.request.CreateEviParam;
import com.timevale.esign.paas.tech.bean.request.FilePdfParam;
import com.timevale.esign.paas.tech.bean.request.OrgSignParam;
import com.timevale.esign.paas.tech.bean.request.OrganizeParam;
import com.timevale.esign.paas.tech.bean.request.PersonParam;
import com.timevale.esign.paas.tech.bean.request.SignFilePdfParam;
import com.timevale.esign.paas.tech.bean.result.CreateEviResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.QueryEviResult;
import com.timevale.esign.paas.tech.bean.result.VerifyPdfResult;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.IdNoTypeEnum;
import com.timevale.esign.paas.tech.enums.OrganRegTypeEnum;
import com.timevale.esign.paas.tech.enums.SignType;
import com.util.Client;
import com.util.data.ModifyDate;
import com.util.file.FileUtil;

import com.util.operation.Operations;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FileDigestSignVerifyQueryEviTest { // 平台用户签+验签+出证
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String FILE_WITH_EDIT_PASSWORD = "./input/带编辑密码表单文件.pdf";
    private static final String FILE_WITH_OPEN_PASSWORD = "./input/带打开密码表单文件.pdf";
    private static final String OUTPUT = "./output";
    private String sealData;
    private String orgAccountId;
    private String psnAccountId;
    private String orgName = "esigntest余爱兰经营的个体工商户";
    private String orgCode = "9100000087664478AN";
    private String psnName = "宣刚义";
    private String idNo = "340822199712270158";
    private ServiceClient serviceClient;

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        sealData = Client.getTimevaleData().getSealData();
        OrganizeParam OrganizeParam = new OrganizeParam();
        OrganizeParam.setRegType(OrganRegTypeEnum.MERGE);
        OrganizeParam.setOrgCode(orgCode);
        OrganizeParam.setName(orgName);
        serviceClient = Client.getClient(env, Client.getTimevaleData().getProject());
        orgAccountId = serviceClient.accountService().addAccount(OrganizeParam).getAccountId();
        PersonParam personParam = new PersonParam();
        personParam.setName(psnName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        personParam.setIdNo(idNo);
        psnAccountId = serviceClient.accountService().addAccount(personParam).getAccountId();
    }

    @AfterClass(enabled = true)
    private void clear() {
        File file = new File(OUTPUT);
        for (File f : file.listFiles()) {
            log.info("删除文件{}", f.getName());
            f.delete();
        }
    }

    private PosBean getPosBean() {
        PosBean posBean = new PosBean();
        posBean.setPosPage("1");
        posBean.setPosX(100F);
        posBean.setPosY(100F);
        posBean.setKeyWord("杭州天谷信息科技有限公司");
        posBean.setWidth(100F);
        return posBean;
    }

    @DataProvider
    public Object[][] fileDigestSignVerifyQueryEviTest() {
        return new Object[][] {
            {
                "普通单页签-成功",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-加密文件未传密码",
                FILE_WITH_EDIT_PASSWORD,
                OUTPUT,
                null,
                getPosBean(),
                sealData,
                SignType.Single,
                600013,
                "pdf安全口令错误"
            },
            {
                "普通单页签-加密文件密码错误",
                FILE_WITH_EDIT_PASSWORD,
                OUTPUT,
                "null",
                getPosBean(),
                sealData,
                SignType.Single,
                801013,
                "文件或流不存在或者PDF口令错误"
            },
            {
                "普通单页签-文件设置打开密码",
                FILE_WITH_OPEN_PASSWORD,
                OUTPUT,
                "qqq111!@#",
                getPosBean(),
                sealData,
                SignType.Single,
                801013,
                "文件或流不存在或者PDF口令错误"
            },
            {
                "普通单页签-通过印章data",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-signType为空",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                null,
                60003,
                "signType不能为空"
            },
            {
                "普通单页签-单页签超出页码",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("100"),
                sealData,
                SignType.Single,
                600012,
                "签名页码超出pdf总页数"
            },
            {
                "普通单页签-页码格式为1,2",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,2"),
                sealData,
                SignType.Single,
                1000027,
                "参数异常"
            },
            {
                "普通单页签-页码格式为1-2",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1-2"),
                sealData,
                SignType.Single,
                1000027,
                "参数异常"
            },
            {
                "普通单页签-坐标超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosX(100000).setPosY(100000),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-小于0",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosX(-100).setPosY(-100),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-单页签设置关键字-不生效",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "单页签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "多页签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "多页签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                sealData,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "多页签，页码超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                sealData,
                SignType.Multi,
                600012,
                "3-2"
            },
            {
                "多页签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                sealData,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "骑缝签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "骑缝签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                sealData,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "骑缝签，页码非法",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                sealData,
                SignType.Edges,
                600012,
                "3-2"
            },
            {
                "骑缝签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                sealData,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "关键字签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，不传页码",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage(null),
                sealData,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                sealData,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                sealData,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                sealData,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，没有关键字",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setKeyWord(null);
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Key,
                2003,
                "关键字签章需要关键字"
            },
            {
                "关键字签，关键字不存在",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setKeyWord("张三李四王五赵六");
                        setPosPage("1");
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Key,
                200006,
                "关键字不存在"
            },
            {
                "width为0为默认大小",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(10);
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为0为负数报错",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(-10);
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为200为压缩到159",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(200);
                        setAddSignTime(true);
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为100为显示100",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(91);
                        setAddSignTime(true);
                    }
                }.setPosPage("1"),
                sealData,
                SignType.Single,
                0,
                "成功"
            },
        };
    }

    @Test(dataProvider = "fileDigestSignVerifyQueryEviTest")
    public void fileDigestSignVerifyQueryEviTest(
            String caseName,
            String srcPdfFile,
            String dstPdfFile,
            String password,
            PosBean posBean,
            String sealData,
            SignType signType,
            int errCode,
            String msg)
            throws IOException {
        OrgSignParam orgSignParam = new OrgSignParam();
        SignFilePdfParam signFilePdfParam = new SignFilePdfParam();
        dstPdfFile = dstPdfFile + "/" + caseName + ".pdf";
        signFilePdfParam.setSrcPdfFile(srcPdfFile);
        signFilePdfParam.setDstPdfFile(dstPdfFile);
        signFilePdfParam.setPassword(password);
        List<PosBean> posBeans =
                new ArrayList<PosBean>() {
                    {
                        add(posBean);
                    }
                };
        orgSignParam.setFileBean(signFilePdfParam);
        orgSignParam.setPosBeans(posBeans);
        orgSignParam.setSealData(sealData);
        orgSignParam.setSignType(signType);
        orgSignParam.setAccountId(orgAccountId);
        orgSignParam.setWillingnessAccountId(psnAccountId);
        orgSignParam.setWillingnessId("");
        orgSignParam.setPosBeans(posBeans);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("执行{}，返回{}", caseName, fileDigestSignResult.getMsg());
        // 检查错误码
        Assert.assertEquals(fileDigestSignResult.getErrCode(), errCode);
        // 检查错误文案
        Assert.assertTrue(fileDigestSignResult.getMsg().contains(msg));
        if (errCode == 0) {
            String expectedName;
            VerifyPdfResult verifyPdfResult;
            CreateEviResult createEviResult;
            CreateEviParam createEviParam = new CreateEviParam();
            createEviParam.setNotifyUrl(Operations.getCALL_BACK());
            if (!StringUtils.isEmpty(signFilePdfParam.getDstPdfFile())) {
                if (signFilePdfParam.getDstPdfFile().toUpperCase().contains("PDF")
                        || !("stream.pdf").equals(signFilePdfParam.getFileName())) {
                    String dstFilePath =
                            signFilePdfParam.getFileName().contains("stream.pdf")
                                    ? signFilePdfParam.getDstPdfFile()
                                    : String.format(
                                            "%s%s%s",
                                            signFilePdfParam.getDstPdfFile(),
                                            File.separatorChar,
                                            signFilePdfParam.getFileName());
                    expectedName =
                            dstFilePath.split("\\\\")[dstFilePath.split("\\\\").length - 1].replace(
                                    OUTPUT, "");
                    expectedName = expectedName.substring(1);
                    log.info("指定目标文件名，创建指定的文件名，预计为：{}", expectedName);
                    String actualName =
                            fileDigestSignResult.getDstFilePath()
                                    .split("\\\\")[
                                    fileDigestSignResult.getDstFilePath().split("\\\\").length - 1];
                    log.info("实际创建的文件名为：{}", actualName);
                    // 指定目标文件时根据指定文件名生成文件
                    Assert.assertEquals(actualName, expectedName);
                } else {
                    expectedName =
                            fileDigestSignResult.getDstFilePath()
                                    .split("\\\\")[
                                    fileDigestSignResult.getDstFilePath().split("\\\\").length - 1];
                    log.info("未指定目标文件名，创建的文件名为：{}", expectedName);
                    // 未指定文件名时文件esign开头
                    Assert.assertTrue(expectedName.startsWith("esign"));
                }
                FilePdfParam filePdfParam = new FilePdfParam();
                filePdfParam.setSrcPdfFile(String.format("%s/%s", OUTPUT, expectedName));
                verifyPdfResult = serviceClient.signVerifyService().localVerifyPdf(filePdfParam);
                // 通过文件出证
                createEviParam.setSrcPdfFile(String.format("%s/%s", OUTPUT, expectedName));
            } else {
                // 未传输出地址不生成文件
                Assert.assertNull(fileDigestSignResult.getDstFilePath());
                // 未传输出地址返回文件流
                Assert.assertNotNull(fileDigestSignResult.getStream());
                FilePdfParam filePdfParam = new FilePdfParam();
                filePdfParam.setStreamFile(fileDigestSignResult.getStream());
                verifyPdfResult = serviceClient.signVerifyService().localVerifyPdf(filePdfParam);
                FileUtil.write(fileDigestSignResult.getStream(), OUTPUT + "/stream.pdf");
                // 文件流可以正常生成文件
                Assert.assertTrue(new File(OUTPUT + "/stream.pdf").exists());
                // 通过文件流出证
                createEviParam.setStreamFile(fileDigestSignResult.getStream());
            }
            // 申请出证
            createEviResult = serviceClient.eviService().createEvi(createEviParam);
            // 等待文件上传
            while (createEviResult.getErrCode() == 10000001) {
                log.info("提交出证申请返回：{}", JSONObject.toJSONString(createEviResult));
                sleep(3000);
                createEviResult = serviceClient.eviService().createEvi(createEviParam);
            }
            log.info("提交出证申请返回：{}", JSONObject.toJSONString(createEviResult));
            // 遍历验签结果
            verifyPdfResult
                    .getSignatures()
                    .forEach(
                            signBean -> {
                                // 企业用户自身签证书持有人为企业
                                Assert.assertEquals(signBean.getCert().getCn(), orgName);
                                // 验签通过
                                Assert.assertTrue(signBean.getSignature().isValidate());
                                long signDate =
                                        ModifyDate.date2Timestamp(
                                                signBean.getSignature().getSignDate());
                                long current = System.currentTimeMillis();
                                log.info("签署时间：{}", ModifyDate.timeStamp2Date(signDate));
                                log.info("当前时间：{}", ModifyDate.timeStamp2Date(current));
                                // 签署时间应当与当前时间近似
                                Assert.assertTrue(Math.abs(signDate - current) < 60_000L);
                            });
            // 成功提交出证申请
            Assert.assertNotNull(createEviResult.getEvId());
            Assert.assertEquals(createEviResult.getErrCode(), 0);
            //            Assert.assertTrue(createEviResult.getMsg().contains("成功"));
            // 查询出证结果
            QueryEviResult queryEviResult =
                    serviceClient.eviService().queryEviResult(createEviResult.getEvId());
            // 等待出证成功
            while (queryEviResult.getStatus().equals(0)) {
                log.info("查询出证结果返回：{}", JSONObject.toJSONString(queryEviResult));
                sleep(3000);
                queryEviResult =
                        serviceClient.eviService().queryEviResult(createEviResult.getEvId());
            }
            log.info("查询出证结果返回：{}", JSONObject.toJSONString(queryEviResult));
            Assert.assertEquals(queryEviResult.getErrCode(), 0);
            Assert.assertEquals(createEviResult.getEvId(), queryEviResult.getEvId());
            Assert.assertNotNull(queryEviResult.getFileUrl());
            Assert.assertEquals(queryEviResult.getStatus(), new Integer(1));
            //            Assert.assertTrue(queryEviResult.getMsg().contains("成功"));
        }
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
