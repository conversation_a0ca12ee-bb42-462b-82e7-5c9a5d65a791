package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.api.support.open_apigw.queryFeedBackLog.QueryFeedBackLog;
import com.entity.esign.Account;
import com.entity.esign.CertInfo;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.*;
import com.util.Client;
import com.util.data.param.Params;
import com.util.file.FileUtil;
import com.util.operation.Operations;
import com.util.report.ExtentTestNGIReporterListener;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.Arrays;

@Slf4j
public class CoreChainTest { // 核心链路
    private final boolean enabled = true;

    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String BROKEN_FILE = "./input/文件已损坏.pdf";
    private static final String OUTPUT = "./output/222.pdf";
    private final String oldPsnName = "张三";
    private final String psnIdNo = "340822199712270158";
    private final String psnName = "测试宣刚义";
    private final String psnMobile = "16111111111";
    private final String oldOrgName = "上海灵契软件有限公司";
    private final String orgCode = "91310117MA1J5D3R9Q";
    private final String legalRepName = "杨阔";
    private final String legalRepIdNo = "211202199509211754";
    private final String legalMobile = "17326030275";
    private final String orgName = "上海灵契软件中心";
    private byte[] fileStream;
    private String psnId;
    private String orgId;
    private String legalRepId;
    private ServiceClient serviceClient;
    private String projectOrgName;
    private String willingnessId;
    private String orgSealDataGb;
    private String orgSealData;
    private String psnSealDataGb;
    private String psnSealData;
    private OrgSignParam orgSignParam;
    private static String onlineAuthId;
    private Project project;
    private static String offlineAuthId;
    private final String SELECT_ESIGN = "select * from account where accountUid = '%s';";
    private final String SELECT_OPEN_RAS = "select * from accountcert where accountId = %s;";
    private final String SELECT_PERSON = "select * from person where id = %s;";
    private final String SELECT_ORGANIZE = "select * from organize where id = %s;";

    @BeforeClass
    @Parameters({"env", "title", "branch", "buildId", "secret", "token", "projectName"})
    public void init(
            String env,
            @Optional String title,
            @Optional String branch,
            @Optional String buildId,
            @Optional String secret,
            @Optional String token,
            @Optional String projectName) {
        ExtentTestNGIReporterListener.setEnv(env);
        log.info("当前环境为：{}", env);
        ExtentTestNGIReporterListener.setTitle(title);
        log.info("消息标题为：{}", title);
        ExtentTestNGIReporterListener.setBranch(branch);
        log.info("测试分支为：{}", branch);
        ExtentTestNGIReporterListener.setBuildId(buildId);
        log.info("当前构建号为：{}", buildId);
        ExtentTestNGIReporterListener.setSecret(secret);
        log.info("钉钉机器人secret为：{}", secret);
        ExtentTestNGIReporterListener.setToken(token);
        log.info("钉钉机器人token为：{}", token);
        ExtentTestNGIReporterListener.setProjectName(projectName);
        log.info("当前项目名称为：{}", projectName);
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getProject();
        serviceClient = Client.getClient(env, project);
        projectOrgName = Client.getTimevaleData().getProjectOrgName(project.getId());
        Operations.mock(
                Arrays.asList(
                        legalMobile,
                        psnMobile,
                        "***********",
                        "***********",
                        "***********",
                        "***********"));
        clearAccounts();
        //        Query.setNewDatabase(false);
    }

    @Test(priority = 0)
    public void createPsnAccount() {
        log.info("开始创建个人账户：");
        PersonParam personParam = Params.getPersonParam(psnIdNo, oldPsnName);
        AddAccountResult addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnId = addAccountResult.getAccountId();
        log.info("创建个人用户{}，返回：{}", oldPsnName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
    }

    @Test(priority = 0)
    public void createLegalAccount() {
        log.info("开始创建法人账户：");
        PersonParam personParam = Params.getPersonParam(legalRepIdNo, legalRepName);
        AddAccountResult addAccountResult = serviceClient.accountService().addAccount(personParam);
        log.info("创建法人账号，返回：{}", JSONObject.toJSONString(addAccountResult));
        legalRepId = addAccountResult.getAccountId();
        Assert.assertNotNull(legalRepId);
    }

    @Test(priority = 0)
    public void createOrgAccount() throws IOException {
        log.info("开始创建企业账户：");
        OrganizeParam organizeParam = Params.getOrganizeParam(orgCode, oldOrgName);
        AddAccountResult addAccountResult =
                serviceClient.accountService().addAccount(organizeParam);
        orgId = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", oldOrgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        fileStream = FileUtil.getFileStream(FILE_PATH);
        orgSealData = Params.getOrgSealData(serviceClient, orgName);
    }

    @Test(priority = 1)
    public void updatePsnAccount() {
        Account accountInfo = Operations.getAccountInfo(psnId);
        long accountId = accountInfo.getId();
        log.info("创建的个人账号在用户中心的id为：{}", accountInfo.getId());
        log.info("创建的个人账号在用户中心的id为：{}", accountId);
        Assert.assertEquals(accountInfo.getName(), oldPsnName);
        Assert.assertEquals(accountInfo.getIdCard(), psnIdNo);
        CertInfo certInfo = Operations.getCertInfo(accountId);
        String raidBeforeUpdate = certInfo.getRaid();
        String snBeforeUpdate = certInfo.getSn();
        log.info("创建的个人账号证书id为：{}", raidBeforeUpdate);
        log.info("创建的个人账号证书持有人为：{}", certInfo.getCertName());
        Assert.assertEquals(certInfo.getCertName(), oldPsnName);
        log.info("开始更新个人账户：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        queryAccountInfoParam.setEncrypt(true);
        String name =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getName();
        log.info("查询证件号为{}的个人用户，名字为{}", psnIdNo, name);
        log.info("名字和创建时一致:");
        Assert.assertEquals(name, oldPsnName);
        UpdatePersonParam updatePersonParam = new UpdatePersonParam();
        updatePersonParam.setAccountId(psnId);
        updatePersonParam.setName(psnName);
        serviceClient.accountService().updateAccount(updatePersonParam);
        log.info("通过accountId查询个人账号信息：");
        name = serviceClient.accountService().getAccountInfo(psnId, true).getName();
        log.info("将{}用户，名字修改为{}", psnId, name);
        Assert.assertEquals(name, psnName);
        AddSealResult addSealResult =
                serviceClient
                        .templateSealService()
                        .createPsnSeal(
                                psnId,
                                null,
                                PersonTemplateType.BORDERLESS,
                                SealColor.RED,
                                StampRuleEnum.SEAL_ONE);
        log.info("更新后根据accountId创建个人印章返回：{}", JSONObject.toJSONString(addSealResult));
        Assert.assertNotNull(addSealResult.getSealData());
        accountInfo = Operations.getAccountInfo(psnId);
        log.info("更新后个人账号在用户中心的id不变：{}", accountInfo.getId());
        Assert.assertEquals(accountId, accountInfo.getId().longValue());
        certInfo = Operations.getCertInfo(accountInfo.getId());
        String raidAfterUpdate = certInfo.getRaid();
        String snAfterUpdate = certInfo.getSn();
        log.info("更新后个人账号证书id为：{}", raidAfterUpdate);
        log.info("更新后个人账号证书持有人为：{}", certInfo.getCertName());
        Assert.assertEquals(certInfo.getCertName(), psnName);
        Assert.assertNotEquals(raidAfterUpdate, raidBeforeUpdate);
        Assert.assertNotEquals(snAfterUpdate, snBeforeUpdate);
        psnSealData = Params.getPsnSealData(serviceClient, psnName);
    }

    @Test(priority = 1)
    // 更新查找账号
    public void updateOrgAccount() {
        log.info("开始更新企业账户：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        String name =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getName();
        log.info("查询证件号为{}的企业用户，名字为{}", orgCode, name);
        log.info("名字和创建时一致：");
        Assert.assertEquals(name, oldOrgName);
        UpdateOrganizeParam updateOrganizeParam = new UpdateOrganizeParam();
        updateOrganizeParam.setAccountId(orgId);
        updateOrganizeParam.setName(orgName);
        serviceClient.accountService().updateAccount(updateOrganizeParam);
        name = serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getName();
        log.info("将accountId为{}的企业用户，名字修改为{}", orgId, name);
        Assert.assertEquals(name, orgName);
        orgSealData = Params.getOrgSealData(serviceClient, orgName);
        log.info(orgSealData);
    }

    @Test(dependsOnMethods = {"updatePsnAccount", "updateOrgAccount"})
    public void createSeals() {
        log.info("开始创建国标印章：");
        orgSealDataGb =
                Operations.getSealDataGb(serviceClient, AccountTypeEnum.ORGAN, orgId, orgName);
        log.info("成功创建{}的国标印章。", orgName);
        psnSealDataGb =
                Operations.getSealDataGb(serviceClient, AccountTypeEnum.PERSON, psnId, psnName);
        log.info("成功创建{}的国标印章。", psnName);
    }

    @Test(
            dependsOnMethods = {"updateOrgAccount"},
            enabled = enabled)
    public void platformSignByOrgSeal() {
        log.info("开始通过文件路径进行平台自身签：");
        PlatformSignParam platformSignParam = Params.getPlatformSignParam();
        platformSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("调用平台自身签，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNotNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNull(fileDigestSignResult.getStream());
        log.info("走文件路径出证：");
        Operations.checkEvi(project, serviceClient, OUTPUT, projectOrgName);
        log.info("走文件路径验签：");
        Operations.verifyPdf(project, serviceClient, OUTPUT, projectOrgName);
        log.info("开始通过文件流进行平台自身签：");
        platformSignParam = Params.getPlatformSignParam(fileStream);
        platformSignParam.getFileBean().setSrcPdfFile(null);
        platformSignParam.setSealId(
                Client.getTimevaleData().getOrgSealIdByProjectId(project.getId()));
        fileDigestSignResult = serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("调用平台自身签，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNotNull(fileDigestSignResult.getStream());
        log.info("走文件流出证：");
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        log.info("走文件流验签：");
        Operations.verifyPdf(
                project, serviceClient, fileDigestSignResult.getStream(), projectOrgName);
    }

    @Test(priority = 2, enabled = enabled)
    public void platformSignByLegalSeal() {
        log.info("开始法人印章进行平台自身签-文件流：");
        PlatformSignParam platformSignParam = Params.getPlatformSignParam();
        String platformSealId = Client.getTimevaleData().getLegalSealIdByProjectId(project.getId());
        platformSignParam.setSealId(platformSealId);
        platformSignParam.getFileBean().setDstPdfFile(null);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("调用平台自身签，返回{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNotNull(fileDigestSignResult.getStream());
        log.info("走文件流出证：");
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        log.info("走文件流验签：");
        Operations.verifyPdf(
                project,
                serviceClient,
                fileDigestSignResult.getStream(),
                Client.getTimevaleData()
                        .getProjectLegalName(Client.getTimevaleData().getProject().getId()));
        log.info("开始法人印章进行平台自身签-文件路径：");
        platformSignParam = Params.getPlatformSignParam();
        platformSignParam.setSealId(
                Client.getTimevaleData().getLegalSealIdByProjectId(project.getId()));
        fileDigestSignResult = serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("调用平台自身签，返回{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNotNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNull(fileDigestSignResult.getStream());
        String legalName =
                Client.getTimevaleData()
                        .getProjectLegalName(Client.getTimevaleData().getProject().getId());
        log.info("走文件路径出证：");
        Operations.checkEvi(project, serviceClient, OUTPUT, legalName);
        log.info("走文件路径验签：");
        Operations.verifyPdf(project, serviceClient, OUTPUT, legalName);
        platformSignParam = Params.getPlatformSignParam(FILE_PATH);
        platformSignParam.setSealId("11111111111");
        fileDigestSignResult = serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("传入不存在的印章id，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 333005);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("未查询到对应印章"));
    }

    @Test(priority = 3, enabled = enabled)
    public void authPlatformWithoutAuth() {
        log.info("未进行平台授权进行签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setAccountId(orgId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做平台授权，无法进行平台授权签署", orgName);
    }

    @Test(priority = 2, enabled = enabled)
    public void legalSignByLegalRepresentative() {
        log.info("法定代表人免授权签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setWillingnessAccountId(legalRepId);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setLegalRepSign(true);
        orgSignParam.setSealData(orgSealData);
        willingnessId =
                Operations.getWillingnessId(project, legalRepIdNo, legalMobile, legalRepName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做平台授权，使用法人签署，返回:{}", orgName, fileDigestSignResult.getMsg());
        log.info(JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertFalse(fileDigestSignResult.isContrastLegalRepInfo(), "不需要扣费的扣费了");
        Operations.verifyPdf(project, serviceClient, OUTPUT, orgName);
        Operations.checkEvi(project, serviceClient, OUTPUT, orgName);
        log.info("使用同一个核身id：{}，第二次签署", willingnessId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        log.info(JSONObject.toJSONString(fileDigestSignResult));
        log.info("企业{}未做平台授权，使用法人签署，返回:{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertFalse(fileDigestSignResult.isContrastLegalRepInfo(), "不需要扣费的扣费了");
        Operations.verifyPdf(project, serviceClient, OUTPUT, orgName);
        Operations.checkEvi(project, serviceClient, OUTPUT, orgName);
        log.info("使用同一个核身id：{}，第三次签署", willingnessId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        log.info(JSONObject.toJSONString(fileDigestSignResult));
        log.info("企业{}未做平台授权，使用法人签署，返回:{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
    }

    @Test(priority = 2, enabled = enabled)
    public void legalSignWithoutWillingnessAccountId() {
        log.info("法定代表人未做核身无法签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setLegalRepSign(true);
        orgSignParam.setWillingnessAccountId(null);
        orgSignParam.setWillingnessId(null);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做平台授权，无签署人信息，进行法人签署，返回：{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(
                fileDigestSignResult
                        .getMsg()
                        .contains("法人签署需要传入accountId、willingnessAccountId、willingnessId"));
    }

    @Test(priority = 2, enabled = enabled)
    public void normalSignByLegalRepresentative() {
        log.info("法定代表人未做授权时进行普通签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setWillingnessAccountId(legalRepId);
        willingnessId =
                Operations.getWillingnessId(project, legalRepIdNo, legalMobile, legalRepName, "");
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做平台授权，未选择法人签署，无法签署，返回：{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
    }

    @Test(priority = 2, enabled = enabled)
    public void legalSignedByNormalPerson() {
        log.info("普通用户做法人签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        log.info("使用个人账号：{}", psnId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        log.info("使用企业账号：{}", orgId);
        orgSignParam.setLegalRepSign(true);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("普通用户无法进行企业法定代表人签署，返回:{}", JSONObject.toJSONString(fileDigestSignResult));
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 339003);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("信息比对不通过"));
        Assert.assertTrue(fileDigestSignResult.isContrastLegalRepInfo());
    }

    @Test(priority = 4, enabled = enabled)
    public void authPlatformOfflineAuthSign() {
        log.info("平台线下授权后签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        log.info("发起线下授权：");
        offlineAuthId = Operations.offlineAuth(serviceClient, 1, orgId, psnId);
        log.info("同意授权：");
        Operations.auditing(project, offlineAuthId, "通过", "同意授权!");
        log.info("等待审批状态同步：");
        Operations.checkAuthStatus(serviceClient, 2, offlineAuthId);
        Operations.sleep(5_000);
        log.info("同意权签后可以签署：");
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(fileDigestSignResult.getAuthId(), offlineAuthId);
        log.info("企业{}通过平台授权，进行平台授权签署", orgName);
        Operations.verifyPdf(project, serviceClient, OUTPUT, orgName);
        Operations.cancelAuth(serviceClient, offlineAuthId);
        log.info("走文件路径出证：");
        Operations.checkEvi(project, serviceClient, OUTPUT, orgName + ",授权流程id：" + offlineAuthId);
    }

    @Test(priority = 5, enabled = enabled)
    public void agentSignOfflineWithoutAuth() {
        log.info("未进行经办人授权进行签署：");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("{}未做经办人授权，无法进行经办人授权签署，返回：{}", psnName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
    }

    @Test(priority = 7, enabled = enabled)
    public void agentSignOfflineAuth() {
        log.info("同意经办人线下授权时签署：");
        log.info("发起线下授权：");
        offlineAuthId = Operations.offlineAuth(serviceClient, 2, orgId, psnId);
        log.info("同意授权：");
        Operations.auditing(project, offlineAuthId, "通过", "同意授权!");
        log.info("等待审批状态同步：");
        orgSignParam.setSealData(orgSealData);
        Operations.checkAuthStatus(serviceClient, 2, offlineAuthId);
        log.info("同意权签后可以签署：");
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(fileDigestSignResult.getAuthId(), offlineAuthId);
        Assert.assertEquals(
                serviceClient
                        .authService()
                        .queryAuth(fileDigestSignResult.getAuthId())
                        .getAuthType()
                        .intValue(),
                2);
        log.info("企业{}通过经办人授权，进行经办人授权签署", orgName);
        Operations.verifyPdf(project, serviceClient, OUTPUT, orgName);
        Operations.checkEvi(project, serviceClient, OUTPUT, orgName + ",授权流程id：" + offlineAuthId);
    }

    @Test(priority = 8, enabled = enabled)
    public void agentSignOfflineAuthSignByBgSeal() {
        log.info("经办人使用同一个核身id，同时使用国标印章签署：");
        orgSignParam.setSealSpec(SealSpecEnum.GB_SEAL);
        orgSignParam.setSealData(orgSealDataGb);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Operations.verifyPdf(project, serviceClient, OUTPUT, orgName);
        Operations.checkEvi(project, serviceClient, OUTPUT, orgName);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Assert.assertEquals(fileDigestSignResult.getAuthId(), offlineAuthId);
    }

    @Test(priority = 9, enabled = enabled)
    public void agentSignOfflineAuthSignByThirdTime() {
        log.info("经办人使用超出次数限制的核身id无法签署：");
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("appId设置核身id可用两次，第三次使用，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("willingnessId超过使用次数限制"));
    }

    @Test(priority = 10, enabled = enabled)
    public void agentSignOfflineAuthSignResendWillingness() {
        log.info("经办人重新获取核身id后可以签署：");
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        log.info("使用核身id，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getAuthId(), offlineAuthId);
    }

    @Test(priority = 11, enabled = enabled)
    public void cancelAgentOfflineAuth() {
        log.info("取消线下经办人授权：");
        Operations.cancelAuth(serviceClient, offlineAuthId);
    }

    // 认证代替授权模式
    @Test(priority = 12, enabled = enabled)
    public void orgAuthModeWithoutOfflineAuth() {
        log.info("未授权状态且未开启认证代替授权模式无法签署：");
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setSealData(orgSealData);
        log.info("不开启认证代替授权模式，无法签署：");
        Operations.identityOrg(project, "", legalRepName, orgName, orgCode, 1);
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setSealData(orgSealData);
        Operations.identityOrg(project, willingnessId, legalRepName, orgName, orgCode, 2);
        orgSignParam.setWillingnessId(willingnessId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
    }

    @Test(priority = 13, enabled = enabled)
    public void authPlatformWithoutOnlineAuth() {
        log.info("未做授权走文件路径签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做平台授权，无法进行企业授权签署，返回：{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("未做授权走走文件流签署");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setAccountId(orgId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做平台授权，无法进行企业授权签署", orgName);
    }

    @Test(priority = 14, enabled = enabled)
    public void onlineAuth() {
        Account account = Operations.getAccountInfo(orgId);
        CertInfo certInfoBefore = Operations.getCertInfo(account.getId());
        log.info("平台线上授权：");
        onlineAuthId = Operations.onlineAuth(serviceClient, 1, psnMobile, orgId, psnId); // 平台授权
        CertInfo certInfoAfter = Operations.getCertInfo(account.getId());
        Assert.assertEquals(
                certInfoAfter.getModifyDate(), certInfoBefore.getModifyDate(), "老版本在授权后不应该重新发证！");
    }

    @Test(priority = 15, enabled = enabled)
    public void agentSignAfterPlatformAuth() {
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setSealData(orgSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}仅授权给平台，可以做经办人签署，返回授权给平台的authId：{}", orgName, onlineAuthId);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertEquals(fileDigestSignResult.getAuthId(), onlineAuthId);
    }

    @Test(priority = 15, enabled = enabled)
    public void authPlatformWithOnlineAuth() {
        log.info("获取授权后走文件路径签署：");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setAccountId(orgId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNotNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNull(fileDigestSignResult.getStream());
        log.info("企业{}已做平台授权，签署成功", orgName);
        Operations.checkEvi(project, serviceClient, fileDigestSignResult.getDstFilePath(), orgName);
        Operations.verifyPdf(
                project, serviceClient, fileDigestSignResult.getDstFilePath(), orgName);
        log.info("获取授权后走文件流签署，使用国标印章：");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setSealSpec(SealSpecEnum.GB_SEAL);
        orgSignParam.setSealData(orgSealDataGb);
        orgSignParam.setAccountId(orgId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNotNull(fileDigestSignResult.getStream());
        // 走文件流出证&验签
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), orgName);
    }

    @Test(priority = 16, enabled = enabled)
    public void cancelPlatformAuth() {
        log.info("取消平台线上授权：");
        Operations.cancelAuth(serviceClient, onlineAuthId);
    }

    @Test(priority = 17, enabled = enabled)
    public void authPlatformAfterCancel() {
        log.info("取消授权后走文件路径签署：");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setAccountId(orgId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做平台授权，无法进行企业授权签署", orgName);
        log.info("取消授权后走文件流签署：");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做平台授权，无法进行企业授权签署", orgName);
    }

    @Test(priority = 18, enabled = enabled)
    public void agentSignWithoutAuth() {
        log.info("未做授权走文件路径签署：");
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做经办人授权，无法进行经办人授权签署，返回：{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("未做授权走文件流签署：");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setSealData(orgSealData);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做经办人授权，无法进行经办人授权签署", orgName);
    }

    @Test(priority = 19, enabled = enabled)
    public void agentSignOnlineAuth() {
        log.info("经办人线上授权：");
        onlineAuthId = Operations.onlineAuth(serviceClient, 2, psnMobile, orgId, psnId);
    }

    @Test(priority = 20, enabled = enabled)
    public void agentSignWithBrokenFile() {
        log.info("授权后走文件路径签署-文件已损坏：");
        orgSignParam = Params.getOrgSignParam(BROKEN_FILE);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 801013);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("PDF打开失败"));
        log.info("已损坏的文件无法签署，返回：{}", fileDigestSignResult.getMsg());
        log.info("此次的核身id为{}", orgSignParam.getWillingnessId());
    }

    @Test(priority = 21, enabled = enabled)
    public void agentSignWithWillingnessId() {
        log.info("使用上次签署失败的核身id继续签署：");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setWillingnessId(willingnessId); // 使用上次失败的核身id
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNotNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNull(fileDigestSignResult.getStream());
        Assert.assertEquals(fileDigestSignResult.getAuthId(), onlineAuthId);
        log.info(
                "企业{}授权给经办人{}后，使用上次失败的核身id，签署成功，返回授权给经办人的authId：{}",
                orgName,
                psnName,
                onlineAuthId);
        log.info("此次的核身id为{}", orgSignParam.getWillingnessId());
        Operations.checkEvi(project, serviceClient, fileDigestSignResult.getDstFilePath(), orgName);
        Operations.verifyPdf(
                project, serviceClient, fileDigestSignResult.getDstFilePath(), orgName);
    }

    @Test(priority = 22, enabled = enabled)
    public void agentSignWithTwiceWillingnessId() {
        log.info("同一个意愿多次使用：");
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("appId设置核身id可用两次，第二次使用时，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Assert.assertEquals(fileDigestSignResult.getAuthId(), onlineAuthId);
    }

    @Test(priority = 23, enabled = enabled)
    public void agentSignOnlineAuthSignByThirdTime() {
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("appId设置核身id可用两次，第三次使用时，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("willingnessId超过使用次数限制"));
    }

    @Test(priority = 24, enabled = enabled)
    public void agentSignOnlineAuthSignResendWillingness() {
        log.info("重新获取核身id后走文件流签署：");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setSealData(orgSealData);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNotNull(fileDigestSignResult.getStream());
        Assert.assertEquals(fileDigestSignResult.getAuthId(), onlineAuthId);
        log.info("企业{}授权给经办人{}后，签署返回：{}", orgName, psnName, fileDigestSignResult.getMsg());
        log.info("根据文件流出证：");
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), orgName);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        log.info("appId设置核身id可用两次，第二次使用时返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Assert.assertEquals(fileDigestSignResult.getAuthId(), onlineAuthId);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        log.info("appId设置核身id可用两次，第三次使用时返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("willingnessId超过使用次数限制"));
    }

    @Test(priority = 25, enabled = enabled)
    public void cancelAgentOnlineAuth() {
        log.info("取消经办人授权：");
        Operations.cancelAuth(serviceClient, onlineAuthId);
    }

    @Test(priority = 26, enabled = enabled)
    public void agentSignWithAgentAuthAndPlatformAuth() {
        String platformAuthid =
                Operations.onlineAuth(serviceClient, 1, psnMobile, orgId, psnId); // 授权给平台
        String agentAuthid =
                Operations.onlineAuth(serviceClient, 2, psnMobile, orgId, psnId); // 授权给经办人
        log.info("授权给平台方的id为：{}", platformAuthid);
        log.info("授权给经办人的id为：{}", agentAuthid);
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("线上同时存在授权给经办人和授权给平台方，使用经办人签署后，返回的authId应当为授权给经办人的id：{}", agentAuthid);
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Assert.assertEquals(fileDigestSignResult.getAuthId(), agentAuthid);
        Assert.assertNotEquals(fileDigestSignResult.getAuthId(), platformAuthid);
        Assert.assertEquals(
                serviceClient
                        .authService()
                        .queryAuth(fileDigestSignResult.getAuthId())
                        .getAuthType()
                        .intValue(),
                2);
        Operations.cancelAuth(serviceClient, platformAuthid);
        Operations.cancelAuth(serviceClient, agentAuthid);
    }

    @Test(priority = 27, enabled = enabled)
    public void orgAuthModeWithoutOnlineAuth() {
        log.info("取消授权后走文件路径签署：");
        orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("企业{}未做经办人授权，无法进行经办人授权签署，返回：{}", orgName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("取消授权后走文件流签署：");
        orgSignParam = Params.getOrgSignParam(fileStream);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setSealData(orgSealData);
        fileDigestSignResult = serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        log.info("企业{}未做经办人授权，无法进行经办人授权签署", orgName);
    }

    @Test(priority = 26, enabled = enabled)
    public void personSign() {
        // 个人走文件路径签署-文件已损坏
        PersonSignParam personSignParam = Params.getPersonSignParam(BROKEN_FILE);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setAccountId(psnId);
        personSignParam.setSealData(psnSealData);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().personSign(personSignParam);
        log.info("已损坏的文件无法签署，返回{}", fileDigestSignResult.getMsg());
        log.info("此次的核身id为{}", personSignParam.getWillingnessId());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 801013);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("PDF打开失败"));
        willingnessId = personSignParam.getWillingnessId();
        personSignParam = Params.getPersonSignParam(FILE_PATH);
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setAccountId(psnId);
        personSignParam.setSealData(psnSealData);
        fileDigestSignResult = serviceClient.userSignService().personSign(personSignParam);
        log.info("用户{}使用上次失败的核身id，签署成功：{}", psnName, fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNotNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNull(fileDigestSignResult.getStream());
        Operations.checkEvi(project, serviceClient, fileDigestSignResult.getDstFilePath(), psnName);
        Operations.verifyPdf(
                project, serviceClient, fileDigestSignResult.getDstFilePath(), psnName);
        fileDigestSignResult = serviceClient.userSignService().personSign(personSignParam);
        log.info("appId设置核身id可用两次，第二次使用->{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        fileDigestSignResult = serviceClient.userSignService().personSign(personSignParam);
        log.info("appId设置核身id可用两次，第三次使用->{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("willingnessId超过使用次数限制"));
        // 个人走文件流签署。认证通过后，使用意愿当认证
        personSignParam = Params.getPersonSignParam(fileStream);
        willingnessId =
                Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, willingnessId);
        personSignParam.setWillingnessId(willingnessId);
        personSignParam.setSealData(psnSealData);
        personSignParam.setAccountId(psnId);
        fileDigestSignResult = serviceClient.userSignService().personSign(personSignParam);
        log.info("使用refVerifyID核身，返回->{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertNull(fileDigestSignResult.getDstFilePath());
        Assert.assertNotNull(fileDigestSignResult.getStream());
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), psnName);
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), psnName);
        // 指定印章格式：国标印章，使用新国标印章
        personSignParam = Params.getPersonSignParam(fileStream);
        personSignParam.setSealSpec(SealSpecEnum.GB_SEAL);
        personSignParam.setAccountId(psnId);
        personSignParam.setSealData(psnSealDataGb);
        willingnessId =
                Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, willingnessId);
        personSignParam.setWillingnessId(willingnessId);
        fileDigestSignResult = serviceClient.userSignService().personSign(personSignParam);
        log.info(
                "用户{}指定印章格式：国标印章，使用新国标印章，请求：{}", psnName, JSONObject.toJSONString(personSignParam));
        log.info("用户{}指定印章格式：国标印章，使用新国标印章，返回：{}", psnName, fileDigestSignResult.getMsg());
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), psnName);
    }

    @Test(priority = 99, enabled = enabled)
    public void callback() {
        QueryFeedBackLog queryFeedBackLog = new QueryFeedBackLog();
        queryFeedBackLog
                .setRequestBody(
                        Operations.getCALL_BACK(),
                        Long.toString(System.currentTimeMillis() - 1_000L * 60 * 20),
                        Long.toString(System.currentTimeMillis() + 1_000L * 60 * 20),
                        "identity_psn_end")
                .send();
        Assert.assertFalse(
                queryFeedBackLog
                        .getResponseBody()
                        .getJSONObject("data")
                        .getJSONArray("items")
                        .isEmpty());
        queryFeedBackLog = new QueryFeedBackLog();
        queryFeedBackLog
                .setRequestBody(
                        Operations.getCALL_BACK(),
                        Long.toString(System.currentTimeMillis() - 1_000L * 60 * 20),
                        Long.toString(System.currentTimeMillis() + 1_000L * 60 * 20),
                        "identity_org_end")
                .send();
        Assert.assertFalse(
                queryFeedBackLog
                        .getResponseBody()
                        .getJSONObject("data")
                        .getJSONArray("items")
                        .isEmpty());
        queryFeedBackLog = new QueryFeedBackLog();
        queryFeedBackLog
                .setRequestBody(
                        Operations.getCALL_BACK(),
                        Long.toString(System.currentTimeMillis() - 1_000L * 60 * 20),
                        Long.toString(System.currentTimeMillis() + 1_000L * 60 * 20),
                        "SDK_EVIDENCE_REPORT")
                .send();
        Assert.assertFalse(
                queryFeedBackLog
                        .getResponseBody()
                        .getJSONObject("data")
                        .getJSONArray("items")
                        .isEmpty());
        queryFeedBackLog = new QueryFeedBackLog();
        queryFeedBackLog
                .setRequestBody(
                        Operations.getCALL_BACK(),
                        Long.toString(System.currentTimeMillis() - 1_000L * 60 * 20),
                        Long.toString(System.currentTimeMillis() + 1_000L * 60 * 20),
                        "ORG_SILENCESIGN_AUTH")
                .send();
        Assert.assertFalse(
                queryFeedBackLog
                        .getResponseBody()
                        .getJSONObject("data")
                        .getJSONArray("items")
                        .isEmpty());
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error("", e);
        }
    }

    private void sleep() {
        sleep(5000);
    }

    @Test(priority = 99)
    public void clearAccounts() {
        log.info("开始删除个人账号：");
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
        log.info("开始删除企业账号：");
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        Operations.deleteAccount(serviceClient, queryAccountInfoParam);
    }

    @Test(priority = 100, enabled = enabled)
    public void signAfterClear() {
        PersonSignParam personSignParam = Params.getPersonSignParam(BROKEN_FILE);
        personSignParam.setAccountId(psnId);
        personSignParam.setSealData(psnSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        personSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().personSign(personSignParam);
        log.info("已删除的个人账号进行签署，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 331003);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号信息异常或不存在"));

        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setWillingnessAccountId(legalRepId);
        orgSignParam.setSealData(orgSealData);
        willingnessId =
                Operations.getWillingnessId(project, legalRepIdNo, legalMobile, legalRepName, "");
        orgSignParam.setWillingnessId(willingnessId);
        log.info("已删除的个人账号进行签署，返回：{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 331003);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号信息异常或不存在"));
    }

    @BeforeMethod
    public void clear() {
        Operations.clearFile();
    }
}
