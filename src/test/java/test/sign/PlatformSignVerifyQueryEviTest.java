package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.timevale.esign.paas.tech.bean.bean.PosBean;
import com.timevale.esign.paas.tech.bean.request.CreateEviParam;
import com.timevale.esign.paas.tech.bean.request.FilePdfParam;
import com.timevale.esign.paas.tech.bean.request.PlatformSignParam;
import com.timevale.esign.paas.tech.bean.request.SignFilePdfParam;
import com.timevale.esign.paas.tech.bean.result.CreateEviResult;
import com.timevale.esign.paas.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.paas.tech.bean.result.QueryEviResult;
import com.timevale.esign.paas.tech.bean.result.VerifyPdfResult;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.SignType;
import com.util.Client;
import com.util.data.ModifyDate;
import com.util.file.FileUtil;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang.StringUtils;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PlatformSignVerifyQueryEviTest { // 平台自动签+验签+出证
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private static final String FILE_WITH_EDIT_PASSWORD = "./input/带编辑密码表单文件.pdf";
    private static final String FILE_WITH_OPEN_PASSWORD = "./input/带打开密码表单文件.pdf";
    private static final String OUTPUT = "./output";
    private String sealIdInProjectId;
    private String sealIdFromOtherProjectId;
    private String sealData;
    private String projectOrgName;
    private ServiceClient serviceClient;

    @Parameters({"env"})
    @BeforeClass()
    public void init(String env) {
        Client.setTimevaleData(env);
        serviceClient = Client.getClient(env, Client.getTimevaleData().getProject());
        String projectId = Client.getTimevaleData().getProject().getId();
        sealIdInProjectId = Client.getTimevaleData().getOrgSealIdByProjectId(projectId);
        sealIdFromOtherProjectId =
                Client.getTimevaleData()
                        .getOrgSealIdByProjectId(Client.getTimevaleData().getBzqAppId());
        String bzqId = Client.getTimevaleData().getBzqAppId();
        sealIdFromOtherProjectId = Client.getTimevaleData().getOrgSealIdByProjectId(bzqId);
        sealData = Client.getTimevaleData().getSealData();
        projectOrgName = Client.getTimevaleData().getProjectOrgName(projectId);
    }

    @AfterClass(enabled = true)
    private void clear() {
        File file = new File(OUTPUT);
        for (File f : file.listFiles()) {
            log.info("删除文件{}", f.getName());
            f.delete();
        }
    }

    private PosBean getPosBean() {
        PosBean posBean = new PosBean();
        posBean.setPosPage("1");
        posBean.setPosX(100F);
        posBean.setPosY(100F);
        posBean.setKeyWord("租用者");
        posBean.setWidth(100F);
        return posBean;
    }

    @DataProvider
    public Object[][] platformSignTest() {
        return new Object[][] {
            {
                "普通单页签-通过印章id",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-加密文件密码错误",
                FILE_WITH_EDIT_PASSWORD,
                OUTPUT,
                "null",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Single,
                801013,
                "文件或流不存在或者PDF口令错误"
            },
            {
                "普通单页签-文件设置打开密码",
                FILE_WITH_OPEN_PASSWORD,
                OUTPUT,
                "qqq111!@#",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Single,
                801013,
                "文件或流不存在或者PDF口令错误"
            },
            {
                "普通单页签-通过印章data",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                null,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-同时传了sealId和sealData，取sealData",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                sealData,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-signType为空",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                null,
                60003,
                "signType不能为空"
            },
            {
                "普通单页签-单页签超出页码",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("100"),
                null,
                sealIdInProjectId,
                SignType.Single,
                600012,
                "签名页码超出pdf总页数"
            },
            {
                "普通单页签-页码格式为1,2",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,2"),
                null,
                sealIdInProjectId,
                SignType.Single,
                1000027,
                "参数异常"
            },
            {
                "普通单页签-页码格式为1-2",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1-2"),
                null,
                sealIdInProjectId,
                SignType.Single,
                1000027,
                "参数异常"
            },
            {
                "普通单页签-坐标超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosX(100000).setPosY(100000),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-小于0",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosX(-100).setPosY(-100),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "普通单页签-单页签设置关键字-不生效",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "单页签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "多页签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "多页签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                null,
                sealIdInProjectId,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "多页签，页码超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                null,
                sealIdInProjectId,
                SignType.Multi,
                600012,
                "3-2"
            },
            {
                "多页签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                null,
                sealIdInProjectId,
                SignType.Multi,
                0,
                "成功"
            },
            {
                "骑缝签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "骑缝签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                null,
                sealIdInProjectId,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "骑缝签，页码非法",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                null,
                sealIdInProjectId,
                SignType.Edges,
                600012,
                "3-2"
            },
            {
                "骑缝签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                null,
                sealIdInProjectId,
                SignType.Edges,
                0,
                "成功"
            },
            {
                "关键字签，页码传1",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdInProjectId,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，不传页码",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage(null),
                null,
                sealIdInProjectId,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码传1,3",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("1,3"),
                null,
                sealIdInProjectId,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码超出",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("3-2"),
                null,
                sealIdInProjectId,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，页码为负数",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean().setPosPage("-2"),
                null,
                sealIdInProjectId,
                SignType.Key,
                0,
                "成功"
            },
            {
                "关键字签，没有关键字",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setKeyWord(null);
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Key,
                2003,
                "关键字签章需要关键字"
            },
            {
                "关键字签，关键字不存在",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setKeyWord("张三李四王五赵六");
                        setPosPage("1");
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Key,
                200006,
                "关键字不存在"
            },
            {
                "width为0为默认大小",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(10);
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为0为负数报错",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(-10);
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为200为压缩到159",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(200);
                        setAddSignTime(true);
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "width为100为显示100",
                FILE_PATH,
                OUTPUT,
                "123456",
                new PosBean() {
                    {
                        setPosPage("1");
                        setWidth(91);
                        setAddSignTime(true);
                    }
                }.setPosPage("1"),
                null,
                sealIdInProjectId,
                SignType.Single,
                0,
                "成功"
            },
            {
                "用了其他应用的sealId",
                FILE_PATH,
                OUTPUT,
                "123456",
                getPosBean(),
                null,
                sealIdFromOtherProjectId,
                SignType.Single,
                0,
                "成功"
            },
        };
    }

    @Test(dataProvider = "platformSignTest", enabled = true)
    public void platformSignTest(
            String caseName,
            String srcPdfFile,
            String dstPdfFile,
            String password,
            PosBean posBean,
            String sealData,
            String sealId,
            SignType signType,
            int errCode,
            String msg)
            throws IOException {
        PlatformSignParam platformSignParam = new PlatformSignParam();
        SignFilePdfParam signFilePdfParam = new SignFilePdfParam();
        dstPdfFile = dstPdfFile + "/" + caseName + ".pdf";
        signFilePdfParam.setSrcPdfFile(srcPdfFile);
        signFilePdfParam.setDstPdfFile(dstPdfFile);
        signFilePdfParam.setPassword(password);
        List<PosBean> posBeans =
                new ArrayList<PosBean>() {
                    {
                        add(posBean);
                    }
                };
        platformSignParam.setFileBean(signFilePdfParam);
        platformSignParam.setPosBeans(posBeans);
        platformSignParam.setSealData(sealData);
        platformSignParam.setSealId(sealId);
        platformSignParam.setSignType(signType);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("执行{}，返回{}", caseName, fileDigestSignResult.getMsg());
        // 检查错误码
        Assert.assertEquals(fileDigestSignResult.getErrCode(), errCode);
        // 检查错误文案
        Assert.assertTrue(fileDigestSignResult.getMsg().contains(msg));
        if (errCode == 0) {
            String expectedName;
            VerifyPdfResult verifyPdfResult;
            CreateEviResult createEviResult;
            CreateEviParam createEviParam = new CreateEviParam();
            createEviParam.setNotifyUrl(Operations.getCALL_BACK());
            if (!StringUtils.isEmpty(signFilePdfParam.getDstPdfFile())) {
                if (signFilePdfParam.getDstPdfFile().toUpperCase().contains("PDF")
                        || !("stream.pdf").equals(signFilePdfParam.getFileName())) {
                    String dstFilePath =
                            signFilePdfParam.getFileName().contains("stream.pdf")
                                    ? signFilePdfParam.getDstPdfFile()
                                    : String.format(
                                            "%s%s%s",
                                            signFilePdfParam.getDstPdfFile(),
                                            File.separatorChar,
                                            signFilePdfParam.getFileName());
                    expectedName =
                            dstFilePath.split("\\\\")[dstFilePath.split("\\\\").length - 1].replace(
                                    OUTPUT, "");
                    expectedName = expectedName.substring(1);
                    log.info("指定目标文件名，创建指定的文件名，预计为：{}", expectedName);
                    String actualName =
                            fileDigestSignResult.getDstFilePath()
                                    .split("\\\\")[
                                    fileDigestSignResult.getDstFilePath().split("\\\\").length - 1];
                    log.info("实际创建的文件名为：{}", actualName);
                    // 指定目标文件时根据指定文件名生成文件
                    Assert.assertEquals(actualName, expectedName);
                } else {
                    expectedName =
                            fileDigestSignResult.getDstFilePath()
                                    .split("\\\\")[
                                    fileDigestSignResult.getDstFilePath().split("\\\\").length - 1];
                    log.info("未指定目标文件名，创建的文件名为：{}", expectedName);
                    // 未指定文件名时文件esign开头
                    Assert.assertTrue(expectedName.startsWith("esign"));
                }
                FilePdfParam filePdfParam = new FilePdfParam();
                filePdfParam.setSrcPdfFile(String.format("%s/%s", OUTPUT, expectedName));
                verifyPdfResult = serviceClient.signVerifyService().localVerifyPdf(filePdfParam);
                // 通过文件出证
                createEviParam.setSrcPdfFile(String.format("%s/%s", OUTPUT, expectedName));
            } else {
                // 未传输出地址不生成文件
                Assert.assertNull(fileDigestSignResult.getDstFilePath());
                // 未传输出地址返回文件流
                Assert.assertNotNull(fileDigestSignResult.getStream());
                FilePdfParam filePdfParam = new FilePdfParam();
                filePdfParam.setStreamFile(fileDigestSignResult.getStream());
                verifyPdfResult = serviceClient.signVerifyService().localVerifyPdf(filePdfParam);
                FileUtil.write(fileDigestSignResult.getStream(), OUTPUT + "/stream.pdf");
                // 文件流可以正常生成文件
                Assert.assertTrue(new File(OUTPUT + "/stream.pdf").exists());
                // 通过文件流出证
                createEviParam.setStreamFile(fileDigestSignResult.getStream());
            }
            // 申请出证
            createEviResult = serviceClient.eviService().createEvi(createEviParam);
            // 等待文件上传
            while (createEviResult.getErrCode() == 10000001) {
                log.info("提交出证申请返回：{}", JSONObject.toJSONString(createEviResult));
                sleep(3000);
                createEviResult = serviceClient.eviService().createEvi(createEviParam);
            }
            log.info("提交出证申请返回：{}", JSONObject.toJSONString(createEviResult));
            // 遍历验签结果
            verifyPdfResult
                    .getSignatures()
                    .forEach(
                            signBean -> {
                                // 平台签证书持有人为平台
                                Assert.assertEquals(signBean.getCert().getCn(), projectOrgName);
                                // 验签通过
                                Assert.assertTrue(signBean.getSignature().isValidate());
                                long signDate =
                                        ModifyDate.date2Timestamp(
                                                signBean.getSignature().getSignDate());
                                long current = System.currentTimeMillis();
                                log.info("签署时间：{}", ModifyDate.timeStamp2Date(signDate));
                                log.info("当前时间：{}", ModifyDate.timeStamp2Date(current));
                                // 签署时间应当与当前时间近似
                                Assert.assertTrue(Math.abs(signDate - current) < 60_000L);
                            });
            // 成功提交出证申请
            Assert.assertNotNull(createEviResult.getEvId());
            Assert.assertEquals(createEviResult.getErrCode(), 0);
            Assert.assertTrue(createEviResult.getMsg().contains("成功"));
            // 查询出证结果
            QueryEviResult queryEviResult =
                    serviceClient.eviService().queryEviResult(createEviResult.getEvId());
            // 等待出证成功
            while (queryEviResult.getStatus().equals(0)) {
                log.info("查询出证结果返回：{}", JSONObject.toJSONString(queryEviResult));
                sleep(3000);
                queryEviResult =
                        serviceClient.eviService().queryEviResult(createEviResult.getEvId());
            }
            log.info("查询出证结果返回：{}", JSONObject.toJSONString(queryEviResult));
            Assert.assertEquals(queryEviResult.getErrCode(), 0);
            Assert.assertEquals(createEviResult.getEvId(), queryEviResult.getEvId());
            Assert.assertNotNull(queryEviResult.getFileUrl());
            Assert.assertEquals(queryEviResult.getStatus(), new Integer(1));
            Assert.assertTrue(queryEviResult.getMsg().contains("成功"));
        }
    }

    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error("", e);
        }
    }

    @DataProvider
    public Object[][] keywordPlatformSignTest() {
        return new Object[][] {{"全部关键字", 0}, {"第一个关键字", 1}, {"最后一个关键字", -1}, {"输入非法", 2}};
    }

    @Test(dataProvider = "keywordPlatformSignTest", enabled = true)
    public void keywordPlatformSignTest(String caseName, int signType) {
        PlatformSignParam platformSignParam = new PlatformSignParam();
        SignFilePdfParam signFilePdfParam = new SignFilePdfParam();
        PosBean posBean = new PosBean();
        posBean.setKeyWord("关键字");
        posBean.setKeywordSignType(signType);
        signFilePdfParam.setSrcPdfFile("input/关键字.pdf");
        signFilePdfParam.setDstPdfFile("output/" + caseName + ".pdf");
        List<PosBean> posBeans =
                new ArrayList<PosBean>() {
                    {
                        add(posBean);
                    }
                };
        platformSignParam.setFileBean(signFilePdfParam);
        platformSignParam.setPosBeans(posBeans);
        platformSignParam.setSealId(sealData);
        platformSignParam.setSignType(SignType.Key);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.platformSignService().platformSign(platformSignParam);
        log.info("{}返回{}", caseName, fileDigestSignResult.getMsg());
        Assert.assertEquals(0, fileDigestSignResult.getErrCode());
    }
}
