package test.sign;

import com.alibaba.fastjson2.JSONObject;
import com.entity.esign.Project;
import com.timevale.esign.paas.tech.bean.request.*;
import com.timevale.esign.paas.tech.bean.result.*;
import com.timevale.esign.paas.tech.client.ServiceClient;
import com.timevale.esign.paas.tech.enums.*;
import com.util.Client;
import com.util.data.param.Params;
import com.util.operation.Operations;

import lombok.extern.slf4j.Slf4j;

import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

@Slf4j
public class OrgAuthStrongModeTest {
    private static final String FILE_PATH = "./input/表单文件.pdf";
    private String psnId;
    private final String psnIdNo = "320682199009254924";
    private final String psnName = "测试郑咏悦";
    private final String psnMobile = "16333333333";
    // 实名经办人
    private final String psnIdNoRealName = "513401197605129425";
    private final String psnNameRealName = "测试余爱兰";
    private final String psnMobileRealName = "16777777777";
    // 核身经办人
    private final String psnIdNoVerify = "430527199409152383";
    private final String psnNameVerify = "测试丘巧巧";
    private final String psnMobileVerify = "16222222222";
    private String psnIdVerify;
    private String psnIdRealName;
    private String orgId;
    private final String orgCode = "9100000087664478AN";
    private final String legalRepName = "测试余爱兰";
    private final String legalRepIdNo = "513401197605129425";

    private final String orgName = "esigntest李保辉能力有限公司";
    private final String legalMobile = "16777777777";
    private String legalRepId;
    private ServiceClient serviceClient;
    private String willingnessId;
    private Project project;
    private String orgSealData;

    @BeforeClass
    @Parameters({"env"})
    public void init(String env) {
        Client.setTimevaleData(env);
        project = Client.getTimevaleData().getStrongAuthAndOfflineAuthTransactorWill();
        serviceClient = Client.getClient(env, project);
    }

    @BeforeMethod
    public void clear() {
        Operations.clearFile();
    }

    @Test(priority = 0)
    // 创建账号
    public void createAccount() {
        // 创建账号
        PersonParam personParam = new PersonParam();
        personParam.setIdNo(psnIdNo);
        personParam.setName(psnName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        AddAccountResult addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnId = addAccountResult.getAccountId();
        log.info("创建个人用户{}，返回：{}", psnName, JSONObject.toJSONString(addAccountResult));
        personParam = new PersonParam();
        personParam.setIdNo(psnIdNoRealName);
        personParam.setName(psnNameRealName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnIdRealName = addAccountResult.getAccountId();
        log.info("创建实名经办人用户{}，返回：{}", psnNameRealName, JSONObject.toJSONString(addAccountResult));
        personParam = new PersonParam();
        personParam.setIdNo(psnIdNoVerify);
        personParam.setName(psnNameVerify);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        addAccountResult = serviceClient.accountService().addAccount(personParam);
        psnIdVerify = addAccountResult.getAccountId();
        log.info("创建核身经办人用户{}，返回：{}", psnNameVerify, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        OrganizeParam organizeParam = new OrganizeParam();
        organizeParam.setOrgCode(orgCode);
        organizeParam.setRegType(OrganRegTypeEnum.MERGE);
        organizeParam.setName(orgName);
        addAccountResult = serviceClient.accountService().addAccount(organizeParam);
        orgId = addAccountResult.getAccountId();
        log.info("创建企业用户{}，返回：{}", orgName, JSONObject.toJSONString(addAccountResult));
        Assert.assertEquals(addAccountResult.getErrCode(), 0);
        orgSealData = Params.getOrgSealData(serviceClient, orgName);
        // 创建法人账号
        personParam.setIdNo(legalRepIdNo);
        personParam.setName(legalRepName);
        personParam.setIdNoType(IdNoTypeEnum.MAINLAND);
        addAccountResult = serviceClient.accountService().addAccount(personParam);
        log.info("创建法人账号，返回：{}", JSONObject.toJSONString(addAccountResult));
        legalRepId = addAccountResult.getAccountId();
    }

    @Test(priority = 3, enabled = true)
    public void orgAuthStrongModeWithoutAuth() {
        // 强校验，未做授权，不可以签署
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setWillingnessAccountId(psnId);
        orgSignParam.setSealData(orgSealData);
        willingnessId = Operations.getWillingnessId(project, psnIdNo, psnMobile, psnName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
    }

    @Test(priority = 1, enabled = true)
    public void legalSignByLegalRepresentative() {
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setWillingnessAccountId(legalRepId);
        orgSignParam.setLegalRepSign(true);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        willingnessId =
                Operations.getWillingnessId(project, legalRepIdNo, legalMobile, legalRepName, "");
        orgSignParam.setWillingnessId(willingnessId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        log.info("未开启法人静默签署，不能进行法人签署:{}", fileDigestSignResult.getMsg());
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("当前应用无法人本人签署权限"));
    }

    @Test(priority = 1, enabled = true)
    public void orgAuthStrongModeSignedByRealNamePerson() {
        // 强校验，实名的人作经办人，不可签署
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.setWillingnessAccountId(psnIdRealName);
        orgSignParam.setAccountId(orgId);
        orgSignParam.setSealData(orgSealData);
        willingnessId =
                Operations.getWillingnessId(
                        project, psnIdNoRealName, psnMobileRealName, psnNameRealName, "");
        orgSignParam.setWillingnessId(willingnessId);
        log.info("企业{}实名经办人为{}，可以签署成功", orgName, psnNameRealName);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 335001);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("账号授权记录不存在"));
    }

    @Test(priority = 2, enabled = true)
    public void orgAuthStrongModeSignedByVerifiedPerson() {
        // 强校验，核身的人作为经办人，可以签署
        OrgSignParam orgSignParam = Params.getOrgSignParam(FILE_PATH);
        orgSignParam.getFileBean().setDstPdfFile(null);
        orgSignParam.setWillingnessAccountId(psnIdVerify);
        orgSignParam.setSealData(orgSealData);
        willingnessId =
                Operations.getWillingnessId(
                        project, psnIdNoVerify, psnMobileVerify, psnNameVerify, "");
        Operations.identityOrg(project, willingnessId, legalRepName, orgName, orgCode, 2);
        orgSignParam.setWillingnessId(willingnessId);
        orgSignParam.setAccountId(orgId);
        FileDigestSignResult fileDigestSignResult =
                serviceClient.userSignService().orgSign(orgSignParam);
        Assert.assertEquals(fileDigestSignResult.getErrCode(), 0);
        Assert.assertTrue(fileDigestSignResult.getMsg().contains("成功"));
        Operations.verifyPdf(project, serviceClient, fileDigestSignResult.getStream(), orgName);
        Operations.checkEvi(serviceClient, fileDigestSignResult.getStream());
    }

    @Test(priority = 100)
    public void clearAccount() {
        // 删除个人账号
        QueryAccountInfoParam queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        psnId = serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        Result result = serviceClient.accountService().deleteAccount(psnId);
        Assert.assertEquals(result.getErrCode(), 0);
        log.info("删除个人{}，返回：{}", psnName, JSONObject.toJSONString(result));
        // 删除实名经办人账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNoRealName);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        psnIdRealName =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        result = serviceClient.accountService().deleteAccount(psnIdRealName);
        log.info("删除实名经办人{}，返回：{}", psnNameRealName, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        // 删除核身经办人账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNoVerify);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        psnIdVerify =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        result = serviceClient.accountService().deleteAccount(psnIdVerify);
        log.info("删除核身经办人{}，返回：{}", psnNameVerify, JSONObject.toJSONString(result));
        Assert.assertEquals(result.getErrCode(), 0);
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        orgId = serviceClient.accountService().getAccountInfo(queryAccountInfoParam).getAccountId();
        // 删除企业账号
        result = serviceClient.accountService().deleteAccount(orgId);
        log.info("删除企业用户{}，accountId为{}", orgName, orgId);
        Assert.assertEquals(result.getErrCode(), 0);
        Assert.assertTrue(result.getMsg().contains("成功"));
        // 查询个人账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.PERSON);
        queryAccountInfoParam.setIdNo(psnIdNo);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MAINLAND);
        AccountInfoResult accountInfoResult =
                serviceClient.accountService().getAccountInfo(queryAccountInfoParam);
        log.info("删除个人账号接口返回：{}", JSONObject.toJSONString(accountInfoResult));
        Assert.assertEquals(accountInfoResult.getErrCode(), 331009);
        log.info("证件号为{}的个人用户已被删除", psnIdNo);
        // 查询企业账号
        queryAccountInfoParam = new QueryAccountInfoParam();
        queryAccountInfoParam.setType(AccountTypeEnum.ORGAN);
        queryAccountInfoParam.setIdNo(orgCode);
        queryAccountInfoParam.setIdNoType(AllIdNoTypeEnum.MERGE);
        accountInfoResult = serviceClient.accountService().getAccountInfo(queryAccountInfoParam);
        Assert.assertEquals(accountInfoResult.getErrCode(), 331009);
        log.info("删除企业账号接口返回：{}", JSONObject.toJSONString(accountInfoResult));
        log.info("证件号为{}的企业用户已被删除", orgCode);
    }
}
