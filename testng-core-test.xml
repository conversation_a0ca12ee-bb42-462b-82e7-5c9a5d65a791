<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="title" value="sdk3.0${projectName}测试报告"/>
    <parameter name="branch" value="${branch}"/>
    <parameter name="buildId" value="${buildId}"/>
    <parameter name="secret" value="${secret}"/>
    <parameter name="token" value="${token}"/>
    <parameter name="projectName" value="${projectName}"/>
    <listeners>
        <listener class-name="com.util.report.ExtentTestNGIReporterListener"/>
    </listeners>
    <parameter name="outputDirectory" value="test-output/"/>
    <test verbose="1" name="快捷签核心链路测试">
        <classes>
            <class name="test.sign.CoreChainTest">
            </class>
        </classes>
    </test>
    <test verbose="1" name="强校验" parallel="none">
        <classes>
            <class name="test.sign.OrgAuthStrongModeTest"/>
        </classes>
    </test>
    <test verbose="1" name="弱校验" parallel="none">
        <classes>
            <class name="test.sign.OrgAuthWeakModeTest"/>
        </classes>
    </test>
    <test verbose="1" name="账号" parallel="none">
        <classes>
            <class name="test.account.org.AccountOrgTest"/>
            <class name="test.account.org.DeleteTest"/>
            <class name="test.account.person.AccountTest"/>
        </classes>
    </test>
    <test verbose="1" name="授权" parallel="none">
        <classes>
            <class name="test.auth.CreateAuthTest"/>
        </classes>
    </test>
    <test verbose="1" name="印章" parallel="none">
        <classes>
            <class name="test.seal.official.OfficialSealTest"/>
            <class name="test.seal.official.CreateGbOfficialSealTest"/>
            <class name="test.seal.personal.PersonTemplateSealTest"/>
        </classes>
    </test>
    <test verbose="1" name="个人证书协议" parallel="none">
        <classes>
            <class name="test.cert.PersonCertTest"/>
        </classes>
    </test>
    <test verbose="1" name="企业证书协议" parallel="none">
        <classes>
            <class name="test.cert.OrgCertTest"/>
        </classes>
    </test>
    <test verbose="1" name="证书过期" parallel="none">
        <classes>
            <class name="test.cert.CertRelayTest"/>
        </classes>
    </test>
    <test verbose="1" name="文本签" parallel="none">
        <classes>
            <class name="test.sign.DigitalSignTest">
            </class>
        </classes>
    </test>
</suite>