<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="OfflineLegalAuthTestSuite" thread-count="1" preserve-order="true">
    <parameter name="env" value="test"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="local" value="test"/><!--test:测试，sml:模拟，pro:正式-->
    
    <test verbose="3" name="线下法人授权测试">
        <classes>
            <class name="test.auth.CreateOfflineLegalAuthTest">
                <methods>
                    <!-- 运行所有测试方法 -->
                    <include name="createOfflineLegalAuthTest"/>
                    <include name="uploadLegalAuthFileExceptionTest"/>
                    <include name="testCreateOfflineLegalAuthToPlatformComplete"/>
                    <include name="testCreateOfflineLegalAuthToPersonComplete"/>
                    <include name="testCreateOfflineLegalAuthSimple"/>
                    <include name="testCreateOfflineLegalAuthToPersonSimple"/>
                    <include name="uploadLegalAuthFileByPathTest"/>
                    <!-- 异常测试 -->
                    <include name="createOfflineLegalRepExceptTest"/>
                </methods>
            </class>
        </classes>
    </test>
</suite>
