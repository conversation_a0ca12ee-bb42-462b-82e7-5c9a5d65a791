<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="title" value="sdk3.0${projectName}测试报告"/>
    <parameter name="branch" value="${branch}"/>
    <parameter name="buildId" value="${buildId}"/>
    <parameter name="secret" value="${secret}"/>
    <parameter name="token" value="${token}"/>
    <parameter name="projectName" value="${projectName}"/>
    <listeners>
        <listener class-name="com.util.report.ExtentTestNGIReporterListener"/>
    </listeners>
    <test verbose="1" name="debug" parallel="none">
        <classes>
            <class name="test.sign.DigitalSignTest">
            </class>
        </classes>
    </test>
</suite>