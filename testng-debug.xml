<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <listeners>
        <listener class-name="com.util.report.ExtentTestNGIReporterListener"/>
    </listeners>
    <test verbose="1" name="debug" parallel="none">
        <classes>
            <class name="test.auth.CreateOfflineLegalAuthTest">
            </class>
        </classes>
    </test>
</suite>