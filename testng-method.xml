<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Default suite" thread-count="1" preserve-order="true">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="local" value="${local}"/><!--test:测试，sml:模拟，pro:正式-->
    <test verbose="3" name="back test">
        <classes>
            <class name="test.RegisterTest">
                <methods>
                    <include name="test"/>
                </methods>
            </class>
            <class name="test.account.org.AccountOrgTest">
                <methods>
                    <include name="OrganizeParamExceptionTest11"/>
                </methods>
            </class>
        </classes>
    </test>
</suite>