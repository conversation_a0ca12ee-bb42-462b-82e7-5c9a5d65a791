<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>paas</groupId>
    <artifactId>sdk</artifactId>
    <version>1.0.0</version>
    <properties>
        <aspectj.version>1.8.10</aspectj.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <paas.sdk.version>paas-sdk-3.0.20</paas.sdk.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.1</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/httpclient-4.5.1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.5.1</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/httpcore-4.4.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.1</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/httpmime-4.5.1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/log4j-1.2.17.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>timevale</groupId>
            <artifactId>paas-sdk</artifactId>
            <version>${paas.sdk.version}.jar</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/history/${paas.sdk.version}.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/slf4j-api-1.7.5.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>1.7.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/slf4j-log4j12-1.7.5.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/commons-lang-2.5.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.14</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/commons-codec-1.14.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/commons-collections-3.2.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/commons-io-2.5.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.2</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/commons-logging-1.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>2.2</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/core-2.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.2.4</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/gson-2.2.4.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.31</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.4</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.14</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.14.3</version>
        </dependency>
        <dependency>
            <groupId>com.aventstack</groupId>
            <artifactId>extentreports</artifactId>
            <version>3.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.relevantcodes</groupId>
            <artifactId>extentreports</artifactId>
            <version>2.41.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>5.3.23</version> <!-- 根据Spring版本调整 -->
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.30</version>
        </dependency>
        <dependency>
            <groupId>tgtest</groupId>
            <artifactId>tgtest-base</artifactId>
            <version>1.6-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>org.codehaus.mojo</groupId>-->
            <!--                <artifactId>aspectj-maven-plugin</artifactId>-->
            <!--                <version>1.11</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>compile</goal>-->
            <!--                            <goal>test-compile</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.22</version>
                        </path>
                        <!-- Add other annotation processors if needed -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
                <configuration>
                    <suiteXmlFiles>
                        <suiteXmlFile>testng.xml</suiteXmlFile>
                    </suiteXmlFiles>
                    <includes>
                        <include>**/**.java</include>
                    </includes>
                    <testFailureIgnore>true</testFailureIgnore>
                    <argLine>
                        -Xms125m -Xmx125m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC
                        -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication
                        -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./output
                        -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
                    </argLine>
                    <forkMode>never</forkMode>
                    <!-- 使allure-results在target文件夹下 -->
                    <!--<workingDirectory>target/</workingDirectory>-->
                    <!--生成allure-result的目录-->
                    <systemPropertyVariables>
                        <property>
                            <name>results</name>
                            <value>./target/allure-results</value>
                        </property>
                        <property>
                            <name>listener</name>
                            <value>com.timevale.qa.apitest.listener.RetryListener</value>
                        </property>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>