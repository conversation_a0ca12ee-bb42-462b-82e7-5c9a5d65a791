<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<!--<suite name="Default suite" thread-count="1" preserve-order="true">-->
<suite name="sdk3.0测试" thread-count="1" data-provider-thread-count="1">
    <parameter name="env" value="${env}"/><!--test:测试，sml:模拟，pro:正式-->
    <parameter name="title" value="${projectName}线上快速响应"/>
    <parameter name="branch" value="${branch}"/>
    <parameter name="buildId" value="${buildId}"/>
    <parameter name="secret" value="${secret}"/>
    <parameter name="token" value="${token}"/>
    <parameter name="projectName" value="${projectName}"/>
    <listeners>
        <listener class-name="com.util.report.ExtentTestNGIReporterListener"/>
    </listeners>
    <test verbose="1" name="线上校验" parallel="none">
        <classes>
            <class name="test.sign.OnlineTest"/>
        </classes>
    </test>
</suite>